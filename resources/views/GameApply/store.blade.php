@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', [
            'breadcrumbs' => array_merge($formData['breadcrumbs'], [
                    [$formData['screenName'], 'GameApply.index', ['search' => 'on']],
                    $title['breadcrumbs'] . '完了',
            ]),
    ])
    <!-- [#gnavi] -->
</header>

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $formData['screenName'] }}：{{ $title['breadcrumbs'] }}完了</h2>

        <div class="center mg-b20">
            <p class="bold tx-20 mg-b6">{{ $title['content'] }}</p>
            ※反映に時間がかかることがありますので、反映されていない場合は少々お待ちください。
        </div>

        <div class="center">
            <a href="{{ URL::route('GameApply.index', ['search' => 'on']) }}" class="button">
                <i class="fa fa-reply"></i>{{ $title['back'] }}
            </a>
        </div>
        <!-- /.class -->
    </div>
</section>
@endsection