@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', [
            'breadcrumbs' => array_merge($formData['breadcrumbs'], [
                    [$formData['screenName'], 'GameApply.index', ['search' => 'on']],
                    '再申請確認',
            ]),
    ])
    <!-- [#gnavi] -->
</header>

@include('partials.errormessage', ['errors' => $errors])

{!! Form::open(['route' => 'GameApply.editupdate', 'id' => 'frm_submit']) !!}
<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $formData['screenName'] }}：再申請確認</h2>

        {!! Form::hidden('id'    , $applyData['id']    ) !!}
        {!! Form::hidden('status', $applyData['status']) !!}
        @include('GameApply.form.confirm')

        <div class="center">
            <a href="{{ URL::route('GameApply.'.$targetUrl['back']) }}" class="button is-medium back-button"><i class="fa fa-reply"></i>修正する</a>
            <a href="{{ URL::route('GameApply.'.$targetUrl['next']) }}" class="button is-primary is-medium is-success submit-button"><i class="fa fa-circle-o"></i>確定する</a>
        </div>

        <!-- /.c-area -->
    </div>
</section>
{!! csrf_field()  !!}
{!! Form::close() !!}
@endsection