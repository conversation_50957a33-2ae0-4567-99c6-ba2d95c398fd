@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', [
            'breadcrumbs' => array_merge($formData['breadcrumbs'], [
                    $formData['screenName'],
            ]),
    ])
    <!-- [#gnavi] -->
</header>

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ last($formData['breadcrumbs']) }}：{{ $formData['screenName'] }}一覧</h2>

        <div class="clearfix mg-b20">
            <div class="float-r">
                <a href="{{ URL::route('GameApply.create') }}" class="button is-primary">申請する</a>
            </div>
        </div>

        @if ($applyDataList->count())
        <!--{ ページャーと表示件数 }-->
        <div class="c-pager">
            <p class="bold tx-16"><i class="fa fa-list-ul"></i>検索結果</p>
            @include('partials.paginator'       , ['paginator' => $applyDataList, 'from' => $pagerView['from'], 'to' => $pagerView['to']])
            @include('partials.paginatorperpage', ['paginator' => $applyDataList])
            <!-- /.c-pager -->
        </div>
        <!--{ / ページャーと表示件数 }-->

        <ul>
            @foreach ($applyDataList as $applyData)
            <li class="mg-b30">
                <table class="c-table info-table">
                <colgroup>
                    <col class="col-xxs">
                    <col class="col-null">
                </colgroup>
                    <tbody>
                        <tr>
                            <th>申請ID</th>
                            <td>{{ $applyData['id'] }}</td>
                        </tr>
                        <tr>
                            <th>タイトル</th>
                            <td>{{ $applyData['game'] }}</td>
                        </tr>
                        <tr>
                            <th>ステータス</th>
                            <td>{{ $namesStatus[$applyData['status']] or '' }}</td>
                        </tr>
                    </tbody>
                </table>
                <div class="clearfix mg-b10">
                    <div class="float-r">
                        <a href="{{ URL::route('GameApply.show', $applyData['id']) }}" class="button">詳細</a>
                    </div>
                </div>
            </li>
            @endforeach
        </ul>

        <!--{ ページャーと表示件数 }-->
        <div class="c-pager">
            @include('partials.paginator'       , ['paginator' => $applyDataList, 'from' => $pagerView['from'], 'to' => $pagerView['to']])
            @include('partials.paginatorperpage', ['paginator' => $applyDataList])
            <!-- /.c-pager -->
        </div>
        <!--{ / ページャーと表示件数 }-->
        @else
        <div class="center bg-lightgrey pd-t20 pd-b20">
            <p class="tx-16"><i class="fa fa-close"></i>検索結果がありません。</p>
        </div>
        @endif

        <!-- /.c-area -->
    </div>
</section>
@endsection