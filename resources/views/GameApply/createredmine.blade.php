@extends('layout')

@section('content')

<header id="gnavi">
    @include('partials.breadcrumbs', [
            'breadcrumbs' => array_merge($formData['breadcrumbs'], [
                    [$formData['screenName'], 'GameApply.index', ['search' => 'on']],
                    '申請',
            ]),
    ])
    <!-- [#gnavi] -->
</header>

@include('partials.errormessage', ['errors' => $errors])

{!! Form::open(['route' => 'GameApply.createconfirm', 'id' => 'frm_submit']) !!}
<section>
    <div class="c-area add-table-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $formData['screenName'] }}：申請</h2>

        @include('GameApply.form.redmine')

        <div class="center">
            <a href="{{ URL::route('GameApply.'.$targetUrl['back']) }}" class="button is-medium back-button"><i class="fa fa-reply"></i>戻る</a>
            <a href="{{ URL::route('GameApply.'.$targetUrl['next']) }}" class="button is-warning is-medium submit-button">次へ</a>
        </div>
        <!-- /.c-area -->
    </div>
</section>
{!! csrf_field()  !!}
{!! Form::close() !!}
@endsection