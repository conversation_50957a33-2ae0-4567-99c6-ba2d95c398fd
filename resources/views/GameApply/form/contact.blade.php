@include('GameApply.form.requestparam', ['filter' => 'contact'])

{{--*/ $reqContact  = isset($applyData['contact']) ?  $applyData['contact'] : [] /*--}}
{{--*/ $reqCount    = count($reqContact)           ?: 1                          /*--}}

<p class="mg-b10 mg-t30 tx-16 bold">▼連絡先情報</p>
@for ($i = 0; $i < $reqCount; $i++)
    {{--*/ $contact = array_shift($reqContact)     ?: []                         /*--}}

    <div class="base-table">
        <table class="c-table">
            <colgroup>
                <col class="col-l">
                <col class="null">
            </colgroup>
            <tbody>
                <tr {{ error_class("contact.{$i}.name") }}>
                    <th>担当者氏名</th>
                    <td>
                        {{--*/ $name = "name"                                        /*--}}
                        {{--*/ $temp = isset($contact[$name]) ? $contact[$name] : '' /*--}}

                        <input type="text" name="contact[{{ $i }}][{{ $name }}]" value="{{ $temp }}" class="w80" />
                    </td>
                </tr>

                <tr {{ error_class("contact.{$i}.tel") }}>
                    <th>担当者TEL</th>
                    <td>
                        {{--*/ $name = "tel"                                         /*--}}
                        {{--*/ $temp = isset($contact[$name]) ? $contact[$name] : '' /*--}}

                        <input type="text" name="contact[{{ $i }}][{{ $name }}]" value="{{ $temp }}" class="w80" />
                    </td>
                </tr>

                <tr {{ error_class("contact.{$i}.mail") }}>
                    <th>担当者メールアドレス</th>
                    <td>
                        {{--*/ $name = "mail"                                        /*--}}
                        {{--*/ $temp = isset($contact[$name]) ? $contact[$name] : '' /*--}}

                        <input type="text" name="contact[{{ $i }}][{{ $name }}]" value="{{ $temp }}" class="w80" />
                    </td>
                </tr>
            </tbody>
            <!-- /.c-table -->
        </table>

        <div class="clearfix mg-b20">
            <div class="float-r">
                <a href="javascript:void(0);" class="button is-danger del-table @if(!$i) is-disabled @endif">連絡先削除</a>
            </div>
        </div>
    </div>
@endfor

<div class="clearfix mg-b10">
    <div class="float-r">
        <a href="javascript:void(0);" class="button is-primary add-table">連絡先追加</a>
    </div>
</div>