@include('GameApply.form.requestparam', ['filter' => 'eocs'])

{{--*/ $reqEocs  = isset($applyData['eocs']) ?  $applyData['eocs'] : [] /*--}}
{{--*/ $reqCount = count($reqEocs)           ?: 1                       /*--}}

<p class="mg-b10 mg-t30 tx-16 bold">▼ソフ倫受理番号詳細</p>
@for ($i = 0; $i < $reqCount; $i++)
    {{--*/ $eocs = array_shift($reqEocs)     ?: []                      /*--}}

    <div class="base-table">
        <table class="c-table">
            <colgroup>
                <col class="col-l">
                <col class="null">
            </colgroup>
            <tbody>
                <tr {{ error_class("eocs.{$i}.company") }}>
                    <th>ソフ倫受理会社名</th>
                    <td>
                        {{--*/ $name = "company"                               /*--}}
                        {{--*/ $temp = isset($eocs[$name]) ? $eocs[$name] : '' /*--}}

                        <input type="text" name="eocs[{{ $i }}][{{ $name }}]" value="{{ $temp }}" class="w80" />
                    </td>
                </tr>

                <tr {{ error_class("eocs.{$i}.title") }}>
                    <th>ソフ倫受理タイトル</th>
                    <td>
                        {{--*/ $name = "title"                                 /*--}}
                        {{--*/ $temp = isset($eocs[$name]) ? $eocs[$name] : '' /*--}}

                        <input type="text" name="eocs[{{ $i }}][{{ $name }}]" value="{{ $temp }}" class="w80" />
                    </td>
                </tr>

                <tr {{ error_class("eocs.{$i}.number") }}>
                    <th>ソフ倫受理番号</th>
                    <td>
                        {{--*/ $name = "number"                                /*--}}
                        {{--*/ $temp = isset($eocs[$name]) ? $eocs[$name] : '' /*--}}

                        <input type="text" name="eocs[{{ $i }}][{{ $name }}]" value="{{ $temp }}" class="w80" />
                    </td>
                </tr>
            </tbody>
            <!-- /.c-table -->
        </table>

        <div class="clearfix mg-b20">
            <div class="float-r">
                <a href="javascript:void(0);" class="button is-danger del-table @if(!$i) is-disabled @endif">項目削除</a>
            </div>
        </div>
    </div>
@endfor

<div class="clearfix mg-b10">
    <div class="float-r">
        <a href="javascript:void(0);" class="button is-primary add-table">項目追加</a>
    </div>
</div>