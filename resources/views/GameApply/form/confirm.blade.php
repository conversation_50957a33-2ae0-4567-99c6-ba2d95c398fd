@include('GameApply.form.requestparam', ['filter' => 'confirm'])

{{--*/ $reqDevice  = isset($applyData['device'] ) ? $applyData['device']  : [] /*--}}
{{--*/ $reqContact = isset($applyData['contact']) ? $applyData['contact'] : [] /*--}}
{{--*/ $reqEocs    = isset($applyData['eocs']   ) ? $applyData['eocs']    : [] /*--}}
{{--*/ $reqRedmine = isset($applyData['redmine']) ? $applyData['redmine'] : [] /*--}}

<p class="mg-b10 mg-t30 tx-16 bold">▼基本情報</p>
<table class="c-table info-table">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr>
            <th>提供会社名</th>
            <td>{{ $applyData['company']   }}</td>
        </tr>
        <tr>
            <th>タイトル</th>
            <td>{{ $applyData['game']      }}</td>
        </tr>
        <tr>
            <th>タイトル（よみがな）</th>
            <td>{{ $applyData['game_ruby'] }}</td>
        </tr>
        <tr>
            <th>タイプ</th>
            <td>{{ $namesType[$applyData['type']] or '' }}</td>
        </tr>
        <tr>
            <th>対応デバイス</th>
            <td>
                @foreach ($reqDevice as $device)
                {{ $namesDevice[$device]   }}&emsp;
                @endforeach
            </td>
        </tr>
        <tr>
            <th>ソフ倫受理済の素材利用</th>
            <td>{{ $namesFlag[$applyData['eocs_material']]  or '' }}</td>
        </tr>
        <tr>
            <th>特商法表記名義</th>
            <td>{{ $namesAsct[$applyData['asct_display']]   or '' }}</td>
        </tr>
        <tr>
            <th>利用プロジェクトURL</th>
            <td>{{ $applyData['redmine_url'] }}</td>
        </tr>
    </tbody>
    <!-- /.c-table -->
</table>

<p class="mg-b10 mg-t30 tx-16 bold">▼連絡先情報</p>
@foreach ($reqContact  as $contact)
<table class="c-table info-table">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr>
            <th>担当者氏名</th>
            <td>{{ $contact['name'] }}</td>
        </tr>
        <tr>
            <th>担当者TEL</th>
            <td>{{ $contact['tel']  }}</td>
        </tr>
        <tr>
            <th>担当者メールアドレス</th>
            <td>{{ $contact['mail'] }}</td>
        </tr>
    </tbody>
</table>
@endforeach

@if ($applyData['asct_display']  == config('forms.GameApply.ownCompany'))
<p class="mg-b10 mg-t30 tx-16 bold">▼特定商取引法に基づく表記</p>
<table class="c-table info-table">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr>
            <th>販売業者の名称</th>
            <td>{{ $applyData['dealer_name']   }}</td>
        </tr>
        <tr>
            <th>販売業者の住所</th>
            <td>{{ $applyData['dealer_addr']   }}</td>
        </tr>
        <tr>
            <th>連絡先　電話番号</th>
            <td>{{ $applyData['dealer_tel']    }}</td>
        </tr>
        <tr>
            <th>連絡先　受付時間</th>
            <td>{{ $applyData['office_hours']  }}</td>
        </tr>
        <tr>
            <th>販売責任者</th>
            <td>{{ $applyData['sales_manager'] }}</td>
        </tr>
        <tr>
            <th>商品の価格</th>
            <td>商品ごとに表示されたポイント価格に基づく</td>
        </tr>
        <tr>
            <th>送料</th>
            <td>なし</td>
        </tr>
        <tr>
            <th>商品代金以外の必要金額</th>
            <td>インターネット接続にかかわる通信回線の費用</td>
        </tr>
        <tr>
            <th>代金支払方法</th>
            <td>ポイントによるお支払い</td>
        </tr>
        <tr>
            <th>支払い時期</th>
            <td>商品購入画面の購入ボタンをおした時</td>
        </tr>
        <tr>
            <th>商品の引き渡し時期</th>
            <td>決済完了後に速やかに提供</td>
        </tr>
        <tr>
            <th>返品の取り扱い方法</th>
            <td>商品の性質上、商品の交換又は返品は受け付けないものとします。</td>
        </tr>
    </tbody>
</table>
@endif

@if ($applyData['eocs_material'] == config('forms.GameApply.exists'))
<p class="mg-b10 mg-t30 tx-16 bold">▼ソフ倫受理番号詳細</p>
@foreach ($reqEocs     as $eocs)
<table class="c-table info-table">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr>
            <th>ソフ倫受理会社名</th>
            <td>{{ $eocs['company'] }}</td>
        </tr>
        <tr>
            <th>ソフ倫受理タイトル</th>
            <td>{{ $eocs['title']   }}</td>
        </tr>
        <tr>
            <th>ソフ倫受理番号</th>
            <td>{{ $eocs['number']  }}</td>
        </tr>
    </tbody>
</table>
@endforeach
@endif

<p class="mg-b10 mg-t30 tx-16 bold">▼申請情報</p>
<table class="c-table">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr {{ error_class('notification_mail') }}>
            <th>通知メールアドレス</th>
            <td>
                {{--*/ $name = "notification_mail"                               /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>
         <tr {{ error_class('notification_mail2') }}>
            <th>通知メールアドレス<span class="tx-10">再入力</span></th>
            <td>
                {{--*/ $name = "notification_mail2"                              /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>
        <tr>
            <th>コメント欄</th>
            <td>
                {{--*/ $name = "comment"                                         /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::textarea($name, $temp, ['cols' => '20', 'rows' => '5']) !!}
            </td>
        </tr>
    </tbody>
    <!-- /.c-table -->
</table>