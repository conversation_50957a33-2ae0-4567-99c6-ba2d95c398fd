@include('GameApply.form.requestparam', ['filter' => 'asct'])

<p class="mg-b10 mg-t30 tx-16 bold">▼特定商取引法に基づく表記</p>
<table class="c-table">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr {{ error_class('dealer_name') }}>
            <th>販売業者の名称</th>
            <td>
                {{--*/ $name = "dealer_name"                                     /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>

        <tr {{ error_class('dealer_addr') }}>
            <th>販売業者の住所</th>
            <td>
                {{--*/ $name = "dealer_addr"                                     /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>

        <tr {{ error_class('dealer_tel') }}>
            <th>連絡先　電話番号</th>
            <td>
                {{--*/ $name = "dealer_tel"                                      /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>

        <tr {{ error_class('office_hours') }}>
            <th>連絡先　受付時間</th>
            <td>
                {{--*/ $name = "office_hours"                                    /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>

        <tr {{ error_class('sales_manager') }}>
            <th>販売責任者</th>
            <td>
                {{--*/ $name = "sales_manager"                                   /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>

        <tr>
            <th>商品の価格</th>
            <td>商品ごとに表示されたポイント価格に基づく</td>
        </tr>

        <tr>
            <th>送料</th>
            <td>なし</td>
        </tr>

        <tr>
            <th>商品代金以外の必要金額</th>
            <td>インターネット接続にかかわる通信回線の諸費用</td>
        </tr>

        <tr>
            <th>代金支払方法</th>
            <td>ポイントによるお支払い</td>
        </tr>

        <tr>
            <th>支払時期</th>
            <td>商品購入画面の購入ボタンをおした時</td>
        </tr>

        <tr>
            <th>商品の引き渡し時期</th>
            <td>決済完了後速やかに提供</td>
        </tr>

        <tr>
            <th>返品の取り扱い方法</th>
            <td>商品の性質上、商品の交換又は返品は受け付けないものとします</td>
        </tr>
    </tbody>
    <!-- /.c-table -->
</table>