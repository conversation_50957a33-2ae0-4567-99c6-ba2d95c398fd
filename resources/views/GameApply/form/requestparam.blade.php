{{--*/ $reqDevice  = isset($applyData['device'] ) ? $applyData['device']  : [] /*--}}
{{--*/ $reqContact = isset($applyData['contact']) ? $applyData['contact'] : [] /*--}}
{{--*/ $reqEocs    = isset($applyData['eocs']   ) ? $applyData['eocs']    : [] /*--}}
{{--*/ $reqRedmine = isset($applyData['redmine']) ? $applyData['redmine'] : [] /*--}}

{{-- ▼基本情報 --}}
@if ($filter  != 'base')
    {!! Form::hidden('company'                 , isset($applyData['company']        ) ? $applyData['company']         : '') !!}
    {!! Form::hidden('game'                    , isset($applyData['game']           ) ? $applyData['game']            : '') !!}
    {!! Form::hidden('game_ruby'               , isset($applyData['game_ruby']      ) ? $applyData['game_ruby']       : '') !!}
    {!! Form::hidden('type'                    , isset($applyData['type']           ) ? $applyData['type']            : '') !!}
    {!! Form::hidden('eocs_material'           , isset($applyData['eocs_material']  ) ? $applyData['eocs_material']   : '') !!}
    {!! Form::hidden('asct_display'            , isset($applyData['asct_display']   ) ? $applyData['asct_display']    : '') !!}
    {!! Form::hidden('redmine_url'             , isset($applyData['redmine_url']    ) ? $applyData['redmine_url']     : '') !!}

    @foreach ($reqDevice   as $i => $device)
    {!! Form::hidden("device[{$i}]"            , $device) !!}
    @endforeach
@endif

{{-- ▼連絡先情報 --}}
@if ($filter  != 'contact')
    {{--*/ $i  = 0 /*--}}

    @foreach ($reqContact  as $contact)
    {!! Form::hidden("contact[{$i}][name]"     , isset($contact['name']   ) ? $contact['name']    : '') !!}
    {!! Form::hidden("contact[{$i}][tel]"      , isset($contact['tel']    ) ? $contact['tel']     : '') !!}
    {!! Form::hidden("contact[{$i}][mail]"     , isset($contact['mail']   ) ? $contact['mail']    : '') !!}
    {!! Form::hidden("contact[{$i}][contact]"  , isset($contact['contact']) ? $contact['contact'] : '') !!}
    {{--*/ $i += 1 /*--}}
    @endforeach
@endif

{{-- ▼特定商取引法に基づく表記 --}}
@if ($filter  != 'asct')
    {!! Form::hidden('dealer_name'             , isset($applyData['dealer_name']  ) ? $applyData['dealer_name']   : '') !!}
    {!! Form::hidden('dealer_addr'             , isset($applyData['dealer_addr']  ) ? $applyData['dealer_addr']   : '') !!}
    {!! Form::hidden('dealer_tel'              , isset($applyData['dealer_tel']   ) ? $applyData['dealer_tel']    : '') !!}
    {!! Form::hidden('office_hours'            , isset($applyData['office_hours'] ) ? $applyData['office_hours']  : '') !!}
    {!! Form::hidden('sales_manager'           , isset($applyData['sales_manager']) ? $applyData['sales_manager'] : '') !!}
@endif

{{-- ▼ソフ倫受理番号詳細 --}}
@if ($filter  != 'eocs')
    {{--*/ $i  = 0 /*--}}

    @foreach ($reqEocs     as $eocs)
    {!! Form::hidden("eocs[{$i}][company]"     , isset($eocs['company']) ? $eocs['company'] : '') !!}
    {!! Form::hidden("eocs[{$i}][title]"       , isset($eocs['title']  ) ? $eocs['title']   : '') !!}
    {!! Form::hidden("eocs[{$i}][number]"      , isset($eocs['number'] ) ? $eocs['number']  : '') !!}
    {{--*/ $i += 1 /*--}}
    @endforeach
@endif

{{-- ▼Redmineプロジェクト情報 --}}
@if ($filter  != 'redmine')
    {{--*/ $i  = 0 /*--}}

    @foreach ($reqRedmine  as $redmine)
    {!! Form::hidden("redmine[{$i}][company]"  , isset($redmine['company']  ) ? $redmine['company']   : '') !!}
    {!! Form::hidden("redmine[{$i}][access_ip]", isset($redmine['access_ip']) ? $redmine['access_ip'] : '') !!}

    @if (isset($redmine['user']))
        {{--*/ $reqUser = $redmine['user'] /*--}}
        {{--*/ $j  = 0 /*--}}

        @foreach ($reqUser as $user)
        {!! Form::hidden("redmine[{$i}][user][{$j}][name]", isset($user['name']) ? $user['name'] : '') !!}
        {!! Form::hidden("redmine[{$i}][user][{$j}][mail]", isset($user['mail']) ? $user['mail'] : '') !!}
        {{--*/ $j += 1 /*--}}
        @endforeach
    @endif
    {{--*/ $i += 1 /*--}}
    @endforeach
@endif

{{-- ▼申請情報 --}}
@if ($filter  != 'confirm')
    {!! Form::hidden('notification_mail'       , isset($applyData['notification_mail']) ? $applyData['notification_mail'] : '') !!}
    {!! Form::hidden('comment'                 , isset($applyData['comment']          ) ? $applyData['comment']           : '') !!}
@endif