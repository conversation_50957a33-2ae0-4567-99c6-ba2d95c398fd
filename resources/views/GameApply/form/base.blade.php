@include('GameApply.form.requestparam', ['filter' => 'base'])

<p class="mg-b10 mg-t30 tx-16 bold">▼基本情報</p>
<table class="c-table">
    <colgroup>
        <col class="col-l">
        <col class="null">
    </colgroup>
    <tbody>
        <tr {{ error_class('company') }}>
            <th>提供会社名</th>
            <td>
                {{--*/ $name = "company"                                         /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80', 'placeholder' => 'DMM GAMES/FANZA GAMES/自社名称のいずれかを入力してください。']) !!}
            </td>
        </tr>

        <tr {{ error_class('game') }}>
            <th>タイトル</th>
            <td>
                {{--*/ $name = "game"                                            /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>

        <tr {{ error_class('game_ruby') }}>
            <th>タイトル（よみがな）</th>
            <td>
                {{--*/ $name = "game_ruby"                                       /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>

        <tr {{ error_class('type') }}>
            <th>タイプ</th>
            <td>
                {{--*/ $name = "type"                                                                           /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : config('forms.GameApply.general') /*--}}

                @foreach ($radioType as $key => $val)
                    {!!    Form::radio($name, $key, $temp == $key, ['id' => "{$name}_{$key}"]) !!}
                    <label for="{{ $name }}_{{ $key }}">{{ $val }}</label>
                @endforeach
            </td>
        </tr>

        <tr {{ error_class('device') }}>
            <th>対応デバイス</th>
            <td>
                {{--*/ $name = "device"                                          /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : [] /*--}}

                @foreach ($checkDevice as $key => $val)
                    {!!    Form::checkbox("{$name}[]", $key, in_array($key, $temp), ['id' => "{$name}_{$key}"]) !!}
                    <label for="{{ $name }}_{{ $key }}">{{ $val }}</label>
                @endforeach
                <p class="tx-red">※SmartPhone、Androidアプリ、APKクラウドは同時に指定できません。</p>
            </td>
        </tr>

        <tr {{ error_class('eocs_material') }}>
            <th>ソフ論受理済の素材利用</th>
            <td>
                {{--*/ $name = "eocs_material"                                                                    /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : config('forms.GameApply.notExists') /*--}}

                @foreach ($radioFlag as $key => $val)
                    {!!    Form::radio($name, $key, $temp == $key, ['id' => "{$name}_{$key}"]) !!}
                    <label for="{{ $name }}_{{ $key }}">{{ $val }}</label>
                @endforeach
            </td>
        </tr>

        <tr {{ error_class('asct_display') }}>
            <th>特商表記名義</th>
            <td>
                {{--*/ $name = "asct_display"                                                                      /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : config('forms.GameApply.ownCompany') /*--}}

                @foreach ($radioAsct as $key => $val)
                    {!!    Form::radio($name, $key, $temp == $key, ['id' => "{$name}_{$key}"]) !!}
                    <label for="{{ $name }}_{{ $key }}">{{ $val }}</label>
                @endforeach
            </td>
        </tr>

        <tr {{ error_class('redmine_url') }}>
            <th>既存のRedmineプロジェクトURL</th>
            <td>
                {{--*/ $name = "redmine_url"                                     /*--}}
                {{--*/ $temp = isset($applyData[$name]) ? $applyData[$name] : '' /*--}}

                {!!    Form::input('text', $name, $temp, ['class' => 'w80']) !!}
            </td>
        </tr>
    </tbody>
    <!-- /.c-table -->
</table>