@extends('layout')

@section('content')

{{--*/ $reqDevice  = isset($applyData['device'] ) ? $applyData['device']  : [] /*--}}
{{--*/ $reqContact = isset($applyData['contact']) ? $applyData['contact'] : [] /*--}}
{{--*/ $reqEocs    = isset($applyData['eocs']   ) ? $applyData['eocs']    : [] /*--}}
{{--*/ $reqRedmine = isset($applyData['redmine']) ? $applyData['redmine'] : [] /*--}}
{{--*/
$reqDevDateDevice = array_filter($applyData->toArray(), function($val, $key) {
    return substr($key, 0, 9) === 'dev_date_' && !empty($val);
}, ARRAY_FILTER_USE_BOTH);
/*--}}

<header id="gnavi">
    @include('partials.breadcrumbs', [
            'breadcrumbs' => array_merge($formData['breadcrumbs'], [
                    [$formData['screenName'], 'GameApply.index', ['search' => 'on']],
                    '詳細',
            ]),
    ])
    <!-- [#gnavi] -->
</header>

<section>
    <div class="c-area">
        <h2 class="c-headline"><i class="fa fa-gamepad fa-purple"></i>{{ $formData['screenName'] }}：詳細</h2>

        @if ($applyData['rejection_reason'])
        <p class="mg-b10 mg-t30 tx-16 bold">▼却下理由</p>
        <div class="tx-red mg-b20">{!! nl2br(e($applyData['rejection_reason'])) !!}</div>
        @endif

        @if ($applyData['status'] == config('forms.GameApply.rejection')
        ||   $applyData['status'] == config('forms.GameApply.approval'))
        <div class="clearfix mg-b20">
            <div class="float-r">
                <a href="{{ URL::route('GameApply.edit', $applyData['id']) }}" class="button is-primary">再申請する</a>
            </div>
        </div>
        @endif

        <div class="area-tab mg-b20">
            <div class="c-tabhead">
                <ul>
                    <li><a href="" class="is-active">基本情報</a></li>

                    @if ($reqDevDateDevice)
                    <li><a href="">デバイス別情報</a></li>
                    @endif

                    @if ($reqContact)
                    <li><a href="">連絡先情報</a></li>
                    @endif

                    @if ($applyData['asct_display']  == config('forms.GameApply.ownCompany'))
                    <li><a href="">特定商取引法に基づく表記</a></li>
                    @endif

                    @if ($reqEocs
                    &&   $applyData['eocs_material'] == config('forms.GameApply.exists'))
                    <li><a href="">ソフ倫受理番号詳細</a></li>
                    @endif

                    @if ($reqRedmine
                    &&   $applyData['redmine_issue'] == config('forms.GameApply.new'))
                    <li><a href="">Redmineプロジェクト情報</a></li>
                    @endif

                    <li><a href="">申請情報</a></li>
                </ul>
                <!-- /.c-tabhead -->
            </div>
            <ul class="c-tabbody">
                <li class="c-tabchild">
                    <table class="c-table info-table">
                        <colgroup>
                            <col class="col-l">
                            <col class="null">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>提供会社名</th>
                                <td>{{ $applyData['company']   }}</td>
                            </tr>
                            <tr>
                                <th>タイトル</th>
                                <td>{{ $applyData['game']      }}</td>
                            </tr>
                            <tr>
                                <th>タイトル（よみがな）</th>
                                <td>{{ $applyData['game_ruby'] }}</td>
                            </tr>
                            <tr>
                                <th>タイプ</th>
                                <td>{{ $namesType[$applyData['type']] or '' }}</td>
                            </tr>
                            <tr>
                                <th>対応デバイス</th>
                                <td>
                                    @foreach ($reqDevice as $device)
                                    {{ $namesDevice[$device]   }}&emsp;
                                    @endforeach
                                </td>
                            </tr>
                            <tr>
                                <th>ソフ倫受理済の素材利用</th>
                                <td>{{ $namesFlag[$applyData['eocs_material']]  or '' }}</td>
                            </tr>
                            <tr>
                                <th>デバイス間データ連携の有無</th>
                                <td>{{ $namesFlag[$applyData['data_linkage']]   or '' }}</td>
                            </tr>
                            <tr>
                                <th>特商法表記名義</th>
                                <td>{{ $namesAsct[$applyData['asct_display']]   or '' }}</td>
                            </tr>
                            <tr>
                                <th>利用プロジェクトURL</th>
                                <td>{{ $applyData['redmine_url'] }}</td>
                            </tr>
                        </tbody>
                        <!-- /.c-table -->
                    </table>
                    <!-- /.c-tabchild -->
                </li>

                @if ($reqDevDateDevice)
                <li class="c-tabchild">
                    @foreach ($reqDevice   as $device)
                    <table class="c-table info-table">
                        <colgroup>
                            <col class="col-m">
                            <col class="null">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>{{ $namesDevice[$device] }}版開発完了予定日</th>
                                <td>{{ $applyData["dev_date_{$device}"] }}</td>
                            </tr>
                            @if ($device != 'emulator')
                                <tr>
                                    <th>{{ $namesDevice[$device] }}版ゲストプレイ</th>
                                    <td>{{ $namesFlag[$applyData["guest_play_{$device}"]] or '' }}</td>
                                </tr>
                            @endif
                        </tbody>
                        <!-- /.c-table -->
                    </table>
                    @endforeach
                    @if (in_array(config('forms.GameApply.pc'), $reqDevice))
                    <table class="c-table info-table">
                        <colgroup>
                            <col class="col-m">
                            <col class="null">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>チャット機能</th>
                                <td>{{ $namesFlag[$applyData['chat']] or '' }}</td>
                            </tr>
                        </tbody>
                        <!-- /.c-table -->
                    </table>
                    @endif
                    <!-- /.c-tabchild -->
                </li>
                @endif

                @if ($reqContact)
                <li class="c-tabchild">
                    @foreach ($reqContact  as $contact)
                    <table class="c-table info-table">
                        <colgroup>
                            <col class="col-s">
                            <col class="null">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>担当者氏名</th>
                                <td>{{ $contact['name'] }}</td>
                            </tr>
                            <tr>
                                <th>担当者TEL</th>
                                <td>{{ $contact['tel']  }}</td>
                            </tr>
                            <tr>
                                <th>担当者メールアドレス</th>
                                <td>{{ $contact['mail'] }}</td>
                            </tr>
                        </tbody>
                        <!-- /.c-table -->
                    </table>
                    @endforeach
                    <!-- /.c-tabchild -->
                </li>
                @endif

                @if ($applyData['asct_display']  == config('forms.GameApply.ownCompany'))
                <li class="c-tabchild">
                    <table class="c-table info-table">
                        <colgroup>
                            <col class="col-s">
                            <col class="null">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>販売業者の名称</th>
                                <td>{{ $applyData['dealer_name']   }}</td>
                            </tr>
                            <tr>
                                <th>販売業者の住所</th>
                                <td>{{ $applyData['dealer_addr']   }}</td>
                            </tr>
                            <tr>
                                <th>連絡先　電話番号</th>
                                <td>{{ $applyData['dealer_tel']    }}</td>
                            </tr>
                            <tr>
                                <th>連絡先　受付時間</th>
                                <td>{{ $applyData['office_hours']  }}</td>
                            </tr>
                            <tr>
                                <th>販売責任者</th>
                                <td>{{ $applyData['sales_manager'] }}</td>
                            </tr>
                            <tr>
                                <th>商品の価格</th>
                                <td>商品ごとに表示されたポイント価格に基づく</td>
                            </tr>
                            <tr>
                                <th>送料</th>
                                <td>なし</td>
                            </tr>
                            <tr>
                                <th>商品代金以外の必要金額</th>
                                <td>インターネット接続にかかわる通信回線の諸費用</td>
                            </tr>
                            <tr>
                                <th>代金支払方法</th>
                                <td>ポイントによるお支払い</td>
                            </tr>
                            <tr>
                                <th>支払時期</th>
                                <td>商品購入画面の購入ボタンを押した後</td>
                            </tr>
                            <tr>
                                <th>商品の引き渡し時期</th>
                                <td>決済完了後に速やかに提供</td>
                            </tr>
                            <tr>
                                <th>返品の取り扱い方法</th>
                                <td>商品の性質上､商品の交換又は返品は受け付けないものとします</td>
                            </tr>
                        </tbody>
                        <!-- /.c-table -->
                    </table>
                    <!-- /.c-tabchild -->
                </li>
                @endif

                @if ($reqEocs
                &&   $applyData['eocs_material'] == config('forms.GameApply.exists'))
                <li class="c-tabchild">
                    @foreach ($reqEocs     as $eocs)
                    <table class="c-table info-table">
                        <colgroup>
                            <col class="col-xs">
                            <col class="null">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>ソフ倫受理会社名</th>
                                <td>{{ $eocs['company'] }}</td>
                            </tr>
                            <tr>
                                <th>ソフ倫受理タイトル</th>
                                <td>{{ $eocs['title']   }}</td>
                            </tr>
                            <tr>
                                <th>ソフ倫受理番号</th>
                                <td>{{ $eocs['number']  }}</td>
                            </tr>
                        </tbody>
                        <!-- /.c-table -->
                    </table>
                    @endforeach
                    <!-- /.c-tabchild -->
                </li>
                @endif

                <li class="c-tabchild">
                    <table class="c-table info-table">
                        <colgroup>
                            <col class="col-xs">
                            <col class="null">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>通知メールアドレス</th>
                                <td>{{ $applyData['notification_mail'] }}</td>
                            </tr>
                            <tr>
                                <th>コメント欄</th>
                                <td>{!! nl2br(e($applyData['comment'])) !!}</td>
                            </tr>
                        </tbody>
                        <!-- /.c-table -->
                    </table>
                    <!-- /.c-tabchild -->
                </li>
            </ul>
            <!-- /.area-tab -->
        </div>

        <div class="center">
            <a href="{{ URL::route('GameApply.index', ['search' => 'on']) }}" class="button is-medium"><i class="fa fa-reply"></i> 戻る</a>
        </div>
        <!-- /.c-area -->
    </div>
</section>
@endsection