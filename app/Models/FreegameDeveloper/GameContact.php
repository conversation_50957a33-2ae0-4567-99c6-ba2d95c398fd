<?php
namespace App\Models\FreegameDeveloper;

class GameContact extends FreegameDeveloper
{
    protected $table   = 'game_contact';

    protected $guarded = ['id'];

    public $timestamps = false;

//*********************************************************************************************************************
    /**
     * 申請ID指定一覧取得
     * @param  integer $applyId
     * @return array
     */
    public function getListByApplyId($applyId)
    {
        if (empty($applyId)) {
            return false;
        }
        return self::where('apply_id', $applyId)->get();
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  integer $id
     * @param  array   $param
     * @return boolean
     */
    public function add($param)
    {
        if (empty($param)) {
            return false;
        }
        return self::create($param);
    }

    /**
     * 更新
     * @param  integer $id
     * @param  array   $param
     * @return boolean
     */
    public function edit($id, $param)
    {
        if (empty($id) || empty($param)) {
            return false;
        }
        return self::where('id', $id)->update($param);
    }

    /**
     * 削除
     * @param  integer $id
     * @return boolean
     */
    public function del($id)
    {
        if (empty($id)) {
            return false;
        }
        return self::where('id', $id)->delete();
    }

    /**
     * 削除：申請ID
     * @param  integer $applyId
     * @return boolean
     */
    public function delByApplyId($applyId)
    {
        if (empty($applyId)) {
            return false;
        }
        return self::where('apply_id', $applyId)->delete();
    }
}
