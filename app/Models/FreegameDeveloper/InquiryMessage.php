<?php
namespace App\Models\FreegameDeveloper;

use DB;
use Illuminate\Support\Facades\Log;

/**
 * お問い合わせテーブル
 */
class InquiryMessage extends FreegameDeveloper
{
    protected $table = 'inquiry_message';

    protected $guarded = [
        'stamp'
    ];

    protected $dates = [
        'send_date',
        'last_send_date'
    ];

    public $timestamps = false;

    /**
     * Get list
     * @param array $condition
     * @param boolean $isCsvDownload
     * @return array
     */
    public function getList($condition = [], $isCsvDownload = false)
    {
        $this->changeModeToUtf8mb4(true);//文字化け対策用

        $select = [
            'im1.id',
            'im1.inquiry_id',
            'im1.app_id',
            'im1.send_date',
            'im1.status',
            'im1.reply_status',
            'im1.device',
            'im1.kind1',
            'im1.kind2',
            'im1.body',
            'im1.user_id',
            'im1.memo',
            'im1.is_memo',
            'im1.is_parent',
            'im1.in_out',
            'large_id',
            'middle_id',
            'small_id',
            '(SELECT max(send_date) FROM inquiry_message im3 WHERE im3.inquiry_id = im1.inquiry_id) AS last_send_date'
        ];

        //カテゴリ
        $query = self::from('inquiry_message AS im1');
        $query->leftJoin('inquiry_categorized_ref', 'im1.inquiry_id', '=', 'inquiry_categorized_ref.inquiry_id');

        if (! empty($condition['app_id'])) {
            if (is_array($condition['app_id'])) {
                $query = $query->whereIn('im1.app_id', $condition['app_id']);
            } else {
                $query = $query->where('im1.app_id', $condition['app_id']);
            }
        }
        if (! empty($condition['inquiry_id'])) {
            $query = $query->where('im1.inquiry_id', $condition['inquiry_id']);
        }
        if (! empty($condition['user_id'])) {
            $query = $query->where('im1.user_id', $condition['user_id']);
        }
        if (! empty($condition['status']) && $condition['status'] != 'all') {
            // ステータスは最初の受信にしか入らないため、
            // 親受信のステータスを参照し抽出する
            $query = $query->where(function ($query) use ($condition) {
                $query->where('im1.status', $condition['status'])
                      ->orWhereExists(function($subQuery) use ($condition) {
                          $subQuery->select(DB::raw(1))
                                   ->from('inquiry_message')
                                   ->whereRaw('inquiry_id = im1.inquiry_id')
                                   ->where('is_parent', '=', 1)
                                   ->where('status', $condition['status']);
                      });
            });
        }
        if (! empty($condition['reply_status'])) {
            // 返信ステータスは最初の受信にしか入らないため、
            // 自身のレコードだけでなくinquiry_idが同じレコードを参照し抽出する
            $query = $query->where(function ($query) use ($condition) {
                $query->whereExists(function($subQuery) use ($condition) {
                          $subQuery->select(DB::raw(1))
                                   ->from('inquiry_message AS im2')
                                   ->whereRaw('im2.inquiry_id = im1.inquiry_id')
                                   ->where('im2.reply_status', $condition['reply_status']);
                      });
            });
        }
        if (! empty($condition['device'])) {
            $query = $query->where('im1.device', $condition['device']);
        }
        if (! empty($condition['kind1'])) {
            $query = $query->where('im1.kind1', $condition['kind1']);
        }
        if (! empty($condition['kind2'])) {
            $query = $query->where('im1.kind2', $condition['kind2']);
        }
        if (isset($condition['is_memo']) && is_numeric($condition['is_memo'])) {
            $query = $query->where('im1.is_memo', $condition['is_memo']);
        }
        // 画面表示は受信のみ、CSVダウンロード時は送受信両方
        if (!$isCsvDownload) {
            $query = $query->where('im1.in_out', 'in');
        }

        if (! empty($condition['staff_name'])) {
            $query->leftJoin('inquiry_reply_staff', 'im1.id', '=', 'inquiry_reply_staff.inquiry_message_id')
            ->where('inquiry_reply_staff.staff_name', $condition['staff_name']);
            $select = array_merge($select, ['"' . $condition['staff_name'] . '" AS staff_name']);
        } else {
            $select = array_merge($select, ['staff_name']);
            $query->leftJoin('inquiry_reply_staff', 'im1.id', '=', 'inquiry_reply_staff.inquiry_message_id');
        }
        if (! empty($condition['from_last_send_date'])) {
            $query = $query->where('im1.send_date', '>=', $condition['from_last_send_date']);
        }
        if (! empty($condition['to_last_send_date'])) {
            $query = $query->where('im1.send_date', '<=', $condition['to_last_send_date']);
        }

        $large_id = $condition['large_id'];
        $middle_id = $condition['middle_id'];
        $small_id = $condition['small_id'];

        //大カテゴリ
        if ($condition['large_id'] === 'not_set') {
            $middle_id = 0;
            $small_id = 0;

            $query->leftJoin('inquiry_category_ref', 'inquiry_category_ref.id', '=', 'inquiry_categorized_ref.large_id');
            $query->where(function ($query) {
                $query->where('large_id', 0)
                    ->orWhere('large_id', null)//設定レコードなし
                    ->orWhere('inquiry_category_ref.id', null);//カテゴリレコードなし
            });
        } elseif (!empty($large_id)) {
            $query->where('large_id', $large_id);
        }
        //中カテゴリ
        if ($middle_id === 'not_set') {
            $small_id = 0;
            $query->leftJoin('inquiry_category_ref', 'inquiry_category_ref.id', '=', 'inquiry_categorized_ref.middle_id');
            $query->where(function ($query) {
                $query->where('middle_id', 0)
                    ->orWhere('middle_id', null)//設定レコードなし
                    ->orWhere('inquiry_category_ref.id', null);//カテゴリレコードなし
            });
        } elseif (!empty($middle_id)) {
            $query->where('middle_id', $middle_id);
        }
        //小カテゴリ
        if ($small_id === 'not_set') {
            $query->leftJoin('inquiry_category_ref', 'inquiry_category_ref.id', '=', 'inquiry_categorized_ref.small_id');
            $query->where(function ($query) {
                $query->where('small_id', 0)
                    ->orWhere('small_id', null)//設定レコードなし
                    ->orWhere('inquiry_category_ref.id', null);//カテゴリレコードなし
            });
        } elseif (!empty($small_id)) {
            $query->where('small_id', $small_id);
        }

        // キーワード
        if (! empty($condition['keyword'])) {
            foreach(preg_split("/[\s]+/", $condition['keyword']) as $keyword_one) {
                $query = $query->whereExists(function ($query) use($keyword_one) {
                    $query->select("id")
                        ->from('inquiry_message AS im4')
                        ->whereRaw('im4.inquiry_id = im1.inquiry_id')
                        ->where('im4.body', 'LIKE', '%'.$keyword_one.'%');
                });
            }
        }

        if (! empty($condition['count'])) {
            $query = $query->selectRaw('COUNT(id)');
        } else {

            if (isset($condition['sort']) && in_array($condition['sort'], [
                'send_date.asc',
                'send_date.desc'
            ])) {
                list ($sort_col, $sort_order) = explode('.', $condition['sort']);
                $query = $query->orderBy('im1.'.$sort_col, $sort_order);
            } else {
                $query = $query->orderBy('last_send_date', 'desc');
                $query = $query->orderBy('im1.id', 'desc');
            }

            $query = $query->selectRaw(implode(',', $select));
        }

        Log::info('SQL:', ['query' => $query->toSql(), 'bindings' => $query->getBindings()]);
        if (! empty($condition['count'])) {//tab
            return $query->count();
        } elseif (! empty($condition['perPage'])) {
            $data = $query->paginate($condition['perPage']);
        } elseif (! empty($condition['take'])) {//CSV
            $data = $query->take($condition['take'])->get();
        } else {
            $data = $query->get();
        }
        $this->changeModeToUtf8mb4(false);//文字化け対策用
        return $data;
    }

    /**
     * Get parent
     * @param string $inquiry_id
     * @return array | bool
     */
    public function getParent($inquiry_id)
    {
        if (empty($inquiry_id)) {
            return false;
        }

        $this->changeModeToUtf8mb4(true);//文字化け対策用
        $query = self::select([
            'inquiry_message.*',
            'inquiry_categorized_ref.large_id',
            'inquiry_categorized_ref.middle_id',
            'inquiry_categorized_ref.small_id',
        ]);

        $data = $query->leftJoin('inquiry_categorized_ref', 'inquiry_message.inquiry_id', '=', 'inquiry_categorized_ref.inquiry_id')
            ->where('inquiry_message.inquiry_id', $inquiry_id)->where('is_parent', 1)->first();

        $this->changeModeToUtf8mb4(false);//文字化け対策用
        return $data;
    }

    /**
     * Get thread list
     * @param string $inquiry_id
     * @return array | bool
     */
    public function getThreadList($inquiry_id)
    {
        if (empty($inquiry_id)) {
            return false;
        }
        $this->changeModeToUtf8mb4(true);//文字化け対策用
        $data = self::leftJoin('inquiry_reply_staff', 'inquiry_message.id', '=', 'inquiry_reply_staff.inquiry_message_id')
            ->where('inquiry_id', $inquiry_id)->where('is_parent', 0)
            ->orderBy('send_date', 'asc')
            ->get();

        $this->changeModeToUtf8mb4(false);//文字化け対策用
        return $data;
    }

    /**
     * Create data inquiry message
     * @param array $data
     * @return int | bool
     */
    public function add($data)
    {
        if (empty($data)) {
            return false;
        }
        $this->changeModeToUtf8mb4(true);//文字化け対策用
        $result = self::insert($data);
        $this->changeModeToUtf8mb4(false);//文字化け対策用
        return $result;
    }

    /**
     * Update Parent
     * @param string $inquiry_id
     * @param array $data
     * @return int | bool
     */
    public function editParent($inquiry_id, $data)
    {
        if (empty($inquiry_id) || empty($data)) {
            return false;
        }
        return self::where('inquiry_id', $inquiry_id)->where('is_parent', 1)->update($data);
    }

    /**
     * Get last reply
     * @param string $inquiry_id
     * @param int $in_out
     * @return object | bool
     */
    public function getLastReply($inquiry_id, $in_out)
    {
        if (empty($inquiry_id)) {
            return false;
        }
        $this->changeModeToUtf8mb4(true);//文字化け対策用
        $data = self::select('body')
            ->where('inquiry_id', $inquiry_id)->where('in_out', $in_out)
            ->orderBy('send_date', 'desc')
            ->first();

        $this->changeModeToUtf8mb4(false);//文字化け対策用
        return $data;
    }

    /**
     * CSV出力　着信/返信の情報を取得
     * @param $inquiry_ids
     * @param $take
     * @return mixed
     */
    public function getThread($inquiry_ids, $take)
    {
        $this->changeModeToUtf8mb4(true);//文字化け対策用
        $query = self::select([
            'id',
            'inquiry_id',
            'is_parent',
            'send_date',
            'last_send_date',
            'body',
            'in_out',
            'staff_name'
        ]);

        //担当者
        $query->leftJoin('inquiry_reply_staff', 'inquiry_message.id', '=', 'inquiry_reply_staff.inquiry_message_id')
            ->whereIn('inquiry_id', $inquiry_ids)
            ->orderBy('inquiry_id', 'desc')
            ->orderBy('send_date', 'asc');
        if ($take) {
            $query->take($take);
        }
        $data = $query->get();

        $this->changeModeToUtf8mb4(false);//文字化け対策用

        return $data;
    }

    /**
     * Get inquiry staff
     * @param int $appId
     * @return array | bool
     */
    public function getInquiryStaff($appId)
    {
        return self::select(['inquiry_message.id', 'inquiry_message.app_id', 'inquiry_reply_staff.staff_name'])
            ->join('inquiry_reply_staff', 'inquiry_reply_staff.inquiry_message_id', '=', 'inquiry_message.id')
            ->where('inquiry_reply_staff.staff_name', '<>', '')
            ->whereRaw('inquiry_message.app_id IN(' . implode(',', $appId) . ')')
            ->groupBy('inquiry_reply_staff.staff_name')
            ->groupBy('inquiry_message.app_id')
            ->orderBy('inquiry_reply_staff.staff_name', 'asc')
            ->get();
    }

    /**
     * getInquiriesToExport get data to export by sql or object
     * @param  array   $request   $request->toArray()
     * @param  boolean $toSql     the return value is string sql or collection result
     * @param  array   &$bindings an array with params to buil native sql
     * @return mixed              an object or string sql
     */
    public function getInquiriesToExport($request, $toSql = true, &$bindings = [])
    {
        config()->set('database.default', $this->connection);
        $csvConfig = config('forms.ReportInquiryReply.exportCsv.defaultValues');

        if (isset($request['app_title']) && $request['app_title']) {
            $app_title = $request['app_title'];
        } else {
            $app_title = $csvConfig['appTitle'];
        }
        $request['begin'] = $request['begin'] . ' 00:00:00';
        $request['end']   = $request['end'] . ' 23:59:59';

        $dateTimeFormat = '%Y/%m/%d';
        if ($request['filter'] === 'byDateTime' || $request['filter'] === 'byHumanInChargeAndDateTime') {
            $dateTimeFormat = '%Y/%m/%d %H:00:00';
        }

        $selectRaw = 'DATE_FORMAT(im.send_date, "' . $dateTimeFormat . '") AS date, "'
            . $app_title .'" AS app_title, device, staff_name, inquiry_id, is_parent, id, in_out';

        if ($request['filter'] === 'byDateTime' || $request['filter'] === 'byHumanInChargeAndDateTime') {
            $selectRaw .= ", DATE_FORMAT(im.send_date, '%H:00:00') AS time";
        }

        $query = self::selectRaw($selectRaw);
        $query->from(DB::raw('inquiry_message AS im'));
        $query->leftJoin('inquiry_reply_staff AS irs', 'im.id', '=', 'irs.inquiry_message_id');
        $query->whereBetween('im.send_date', [$request['begin'], $request['end']]);
        $bindings[] = $request['begin'];
        $bindings[] = $request['end'];

        if ($request['filter'] === 'byHumanInChargeAndDate'
            || $request['filter'] === 'byHumanInChargeAndDateTime') {
            $query->whereRaw('(irs.staff_name = "' . $request['human_in_charge'] . '" OR irs.staff_name IS NULL)');
        }

        if ($request['app_id']) {
            $query->where('im.app_id', '=', $request['app_id']);
            $bindings[] = $request['app_id'];
        }

        $query->orderBy('date');
        $query->orderBy('irs.staff_name');

        if ($toSql) {
            return $query->toSql();
        }
        return $query->get();
    }

    /**
     * CSV出力用データを取得
     * @param  string $sql
     * @param  array $bindings
     * @return object
     */
    public function getExportCsvData($sql, $bindings = [])
    {
        return $this->getSelectStmt($sql, $bindings);
    }

    /**
     * Get data by inquiry id
     * @param string $inquiryId
     * @return object
     */
    public function getByInquiryId($inquiryId)
    {
        return self::select('device')->where('is_parent', '=', 1)->where('inquiry_id', '=', $inquiryId)->first();
    }
}
