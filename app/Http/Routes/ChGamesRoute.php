<?php

Route::group([
    'prefix' => 'chgames'
], function () {
    Route::get(
        '/',
        [
            'as'   => 'ChGames.index',
            'uses' => 'ChGamesController@index',
        ]
    );
    Route::get(
        'index',
        [
            'as'   => 'ChGames.index',
            'uses' => 'ChGamesController@index',
        ]
    );
    Route::any(
        'edit/{id}',
        [
            'as'   => 'ChGames.edit',
            'uses' => 'ChGamesController@edit',
        ]
    );
    Route::post(
        'editconfirm',
        [
            'as'   => 'ChGames.editconfirm',
            'uses' => 'ChGamesController@editconfirm',
        ]
    );
    Route::post(
        'update',
        [
            'as'   => 'ChGames.update',
            'uses' => 'ChGamesController@update',
        ]
    );
    Route::get('board/{id}', [
        'as' => 'ChGames.board',
        'uses' => 'ChGamesController@board',
    ]);
    Route::post('board/generate-url/{id}', [
        'as' => 'ChGames.board.generateUrl',
        'uses' => 'ChGamesController@generateSignedUrl',
    ]);
    Route::post('board/comment/{id}', [
        'as' => 'ChGames.board.comment',
        'uses' => 'ChGamesController@addBoardComment',
    ]);
});
