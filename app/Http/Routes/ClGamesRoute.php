<?php

Route::group([
    'prefix' => 'clgames'
], function () {
    Route::get(
        '/',
        [
            'as' => 'ClGames.index',
            'uses' => 'ClGamesController@index',
        ]
    );
    Route::get(
        'index',
        [
            'as' => 'ClGames.index',
            'uses' => 'ClGamesController@index',
        ]
    );
    Route::get('board/{id}', [
        'as' => 'ClGames.board',
        'uses' => 'ClGamesController@board',
    ]);
    Route::post('board/generate-url/{id}', [
        'as' => 'ClGames.board.generateUrl',
        'uses' => 'ClGamesController@generateSignedUrl',
    ]);
    Route::post('board/comment/{id}', [
        'as' => 'ClGames.board.comment',
        'uses' => 'ClGamesController@addBoardComment',
    ]);
    // 通知先更新
    Route::get('notification/update/{id}/{device}', [
        'as' => 'ClGames.notification.update',
        'uses' => 'ClGamesController@updateNotification',
    ]);
});
