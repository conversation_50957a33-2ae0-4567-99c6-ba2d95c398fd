<?php
Route::group(
    [
        'prefix' => 'ngword/filter/application'
    ],
    function () {
        Route::get(
            '/',
            [
                'as' => 'NgwordFilter.index',
                'uses' => 'NgwordFilterController@index'
            ]
        );

        Route::get(
            'index/',
            [
                'as' => 'NgwordFilter.index',
                'uses' => 'NgwordFilterController@index'
            ]
        );

        Route::any(
            'create/',
            [
                'as'   => 'NgwordFilter.create',
                'uses' => 'NgwordFilterController@create',
            ]
        );

        Route::post(
            'createconfirm/',
            [
                'as'   => 'NgwordFilter.createconfirm',
                'uses' => 'NgwordFilterController@createConfirm',
            ]
        );

        Route::post(
            'store/',
            [
                'as'   => 'NgwordFilter.store',
                'uses' => 'NgwordFilterController@store'
            ]
        );

        Route::any(
            'edit/{id}',
            [
                'as'   => 'NgwordFilter.edit',
                'uses' => 'NgwordFilterController@edit',
            ]
        );

        Route::post(
            'editconfirm/',
            [
                'as'   => 'NgwordFilter.editconfirm',
                'uses' => 'NgwordFilterController@editConfirm',
            ]
        );

        Route::post(
            'update/',
            [
                'as'   => 'NgwordFilter.update',
                'uses' => 'NgwordFilterController@update',
            ]
        );

        Route::any(
            'ngword_exclusions/edit/{id}',
            [
                'as'   => 'NgwordFilter.ngwordexclusionsedit',
                'uses' => 'NgwordFilterController@ngwordExclusionsEdit',
            ]
        );

        Route::post(
            'ngword_exclusions/editconfirm/',
            [
                'as'   => 'NgwordFilter.ngwordexclusionseditconfirm',
                'uses' => 'NgwordFilterController@ngwordExclusionsEditConfirm',
            ]
        );

        Route::post(
            'ngword_exclusions/update/',
            [
                'as'   => 'NgwordFilter.ngwordexclusionsupdate',
                'uses' => 'NgwordFilterController@ngwordExclusionsUpdate',
            ]
        );

        Route::post(
            'ngword_exclusions/add',
            [
                'as'   => 'NgwordFilter.ngwordexclusionsadd',
                'uses' => 'NgwordFilterController@ngwordExclusionsAdd',
            ]
        );

        Route::post(
            'destroy/',
            [
                'as'   => 'NgwordFilter.destroy',
                'uses' => 'NgwordFilterController@destroy',
            ]
        );
    }
);
