<?php

namespace App\Http\Controllers;

use Exception;
use Log;
use Illuminate\Http\Request;
use App\Libs\Apply\ApplyCommon;
use App\Services\ClGamesService;
use App\Services\ApplyPreregistrationService;
use App\Services\ApplyReleaseService;
use App\Services\ApplyService;
use App\Services\BoardService;
use App\Services\ApplyNotificationService;

class ClGamesController extends Controller
{
    protected $clGamesService;

    /** @var ApplyPreregistrationService */
    protected $applyPreregistrationService;

    /** @var ApplyReleaseService */
    protected $applyReleaseService;

    /** @var ApplyService */
    protected $applyService;

    /** @var BoardService */
    protected $boardService;

    /** @var ApplyNotificationService */
    protected $applyNotificationService;

    public function __construct(
        ClGamesService              $clGamesService,
        ApplyPreregistrationService $applyPreregistrationService,
        ApplyReleaseService         $applyReleaseService,
        ApplyService $applyService,
        BoardService $boardService,
        ApplyNotificationService $applyNotificationService)
    {
        parent::__construct();

        $this->clGamesService = $clGamesService;
        $this->applyPreregistrationService = $applyPreregistrationService;
        $this->applyReleaseService = $applyReleaseService;
        $this->applyService = $applyService;
        $this->boardService = $boardService;
        $this->applyNotificationService = $applyNotificationService;
        view()->share($this->clGamesService->getFormData());

        // JS読み込み
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/controller/Games/Common.js';
        $javascriptFileList[] = '/js/apply/games.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);
    }

//*********************************************************************************************************************

    /**
     * 一覧
     * @param Request $request
     * @return view
     */
    public function index(Request $request)
    {
        $appId = $request->input('app_id');
        $device = 'client';
        $tab = $request->get('tab', config('forms.Games.defaultTab'));
        $data = null;

        if (empty($appId)) {
            // メニューからの遷移
            $clAppDataList = $this->clGamesService->getList();
            $data = $clAppDataList->first();
            if (!empty($data)) {
                    $appId = $data->id;    
            }
        } else {
            // タイトルを指定して（セレクトボックスを選択して）遷移
            $clAppDataList = $this->clGamesService->getListByAppId($appId);
            $data = $clAppDataList->first();
        }

        // 事前登録及びリリース申請登録フォームの情報取得
        $preregistration = null;
        $release = null;
        $applyCommonInfomation = null;

        $informationTopics = null;
        $notificationAddress = null;

        if ($tab == 'preregistration' || $tab == 'release') {
            // お知らせの情報取得
            $informationTopics = $this->boardService->getInformationTopics($appId, 'cl_application');
            // 通知メールアドレス取得
            $notificationAddress = $this->applyNotificationService->getNotificationAddress($appId, 'cl_application');

            switch ($tab) {
                case 'preregistration': // 事前登録タブが選択されている場合
                    $preregistration = $this->applyPreregistrationService->getPreregistration($appId, $device);
                    $applyCommonInfomation = $preregistration;
                    break;
                case 'release': // リリース申請タブが選択されている場合
                    $release = $this->applyReleaseService->getRelease($appId, $device);
                    // 事前登録が完了している場合は、事前登録情報クラスをリリース申請情報クラスに設定する
                    if ($release->isPreRegistrationActive()) {
                        $preregistration = $this->applyPreregistrationService->getPreregistration($appId, $device);
                        $release->setPreregistration($preregistration);
                    }
                    $applyCommonInfomation = $release;
                    break;
                default:
                    break;
            }
        }

        $appTitleType = $this->clGamesService->getAppTitleType();

        return view(
            'ClGames.index',
            compact(
                'appId',
                'device',
                'tab',
                'data',
                'appTitleType',
                'applyCommonInfomation',
                'preregistration',
                'release',
                'informationTopics',
                'notificationAddress'
            )
        );
    }

    /**
     * 掲示板モーダル表示
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return \Illuminate\View\View
     */
    public function board(Request $request, $appId)
    {
        if (!$this->isDeveloperApplication($appId)) {
            abort(404);
        }

        // これがないと掲示板表示時にJSでエラーが発生し、JS処理が実行できなくなるので追加
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/readmore.min.js';
        $javascriptFileList[] = '/js/apply/board.js';
        $javascriptFileList[] = '/js/apply/file.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);

        $page = $request->get('page', 1);
        $perPageNum = $request->get('perPage', 10);

        // コメントの取得
        $kind = 'cl_application';
        $paginator = $this->boardService->getMessageBoardContent($request, $appId, $kind, $page, $perPageNum);

        // messageNumberが指定されている場合は該当ページにリダイレクト
        // MEMO: メッセージ削除機能がないことを前提にページ番号を計算しているため削除機能追加時は注意すること
        $messageNumber = $request->get('messageNumber');
        if (!is_null($messageNumber) && is_numeric($messageNumber)) {
            $page = (int) ceil(($paginator->total() - ($messageNumber - 1))  / $perPageNum);
            if ($page > 0) {
                return redirect()->route('ClGames.board', ['id' => $appId, 'page' => $page]);
            }
        }

        $pagerView = $this->boardService->getPagerView($paginator, config('forms.ClGames.pagerLinkNum'));
        $baseRoute = 'ClGames';

        $response = $this->applyService->getTargetInformation($appId, $kind);
        $releaseStatus = !empty($response['releaseStatus']) ? $response['releaseStatus']: '';

        $appTitleType = $this->clGamesService->getAppTitleType();
        $appTitle = $appTitleType[$appId];

        return view('Games.modals.board', compact(
            'appId',
            'paginator',
            'pagerView',
            'baseRoute',
            'appTitle',
            'releaseStatus'
        ));
    }

    /**
     * ファイルアップロード用署名付きURL発行
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return json
     */
    public function generateSignedUrl(Request $request, $appId)
    {
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $uploadFileNum = $request->get('uploadFileNum');
            // ファイルアップロード用の署名付きURLの発行
            $response = $this->boardService->generateSignedUrls($appId, config('forms.ClGames.boardFileUploadDirectory'), $uploadFileNum);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * 掲示板コメント追加
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return json
     */
    public function addBoardComment(Request $request, $appId)
    {
        // 掲示板のコメント追加処理
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $content = $request->get('comment');
            $uploadFileList = $request->get('uploadFileList');
            $directory = config('forms.ClGames.boardFileUploadDirectory');
            $response = $this->boardService->postMessageContent(
                $appId,
                'cl_application',
                $content,
                $uploadFileList,
                $directory
            );

            // メール送信
            $this->registPostCommentSapNotification($appId);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * PF運営宛の掲示板コメント追加の通知メールを送信する
     *
     * @param mixed $id
     * @return void
     */
    private function registPostCommentSapNotification($id){
        // メール送信
        $kind = 'cl_application';
        try{
            $this->applyNotificationService->registPostCommentSapNotification($id, $kind);
        } catch (Exception $e) {
            // 未知のエラー
            Log::error(sprintf("PF運営宛のメール通知に失敗しました。appId:%s kind:%s Exception:%s\n%s", 
                $id, $kind, $e->getMessage(), $e->getTraceAsString()));
        }
    }

    /**
     * デベロッパーの対象アプリかを判定する
     *
     * @param  mixed $appId
     * @return bool
     */
    public function isDeveloperApplication($appId){
        $appTitleType = $this->clGamesService->getAppTitleType();

        return array_key_exists($appId, $appTitleType);
    }
}
