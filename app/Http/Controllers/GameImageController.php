<?php

namespace App\Http\Controllers;

use App\Exceptions\ApplicationImageException;
use App\Http\Requests\ApplicationImageTimerRequest;
use App\Http\Requests\PwaManifestSettingRequest;
use App\Services\ApplicationImageTimerDefaultImageService;
use App\Services\ApplicationImageTimerImageService;
use App\Services\ApplicationImageTimerService;
use App\Services\ApplicationImageActiveService;
use App\Services\PwaManifestService;
use Illuminate\Http\Request;
use App\Services\GameImageService;
use App\Http\Requests\GameImageRequest;
use App\Exceptions\FileUploadException;
use Exception;

class GameImageController extends Controller
{
    protected $gameImageService;
    protected $timerService;
    protected $defaultImageService;
    protected $timerImageService;
    protected $imageActiveService;
    protected $pwaManifestService;

    public function __construct(
        GameImageService $gameImageService,
        ApplicationImageTimerService $imageTimerService,
        ApplicationImageTimerDefaultImageService $defaultImageService,
        ApplicationImageTimerImageService $timerImageService,
        ApplicationImageActiveService $imageActiveService,
        PwaManifestService $pwaManifestService
    ) {
        parent::__construct();

        $this->gameImageService = $gameImageService;
        $this->timerService = $imageTimerService;
        $this->defaultImageService = $defaultImageService;
        $this->timerImageService = $timerImageService;
        $this->imageActiveService = $imageActiveService;
        $this->pwaManifestService = $pwaManifestService;
        view()->share($this->gameImageService->getFormData());
        view()->share($this->timerService->getFormData());
    }

//*********************************************************************************************************************

    /**
     * 一覧
     * @param GameImageRequest $request
     * @param integer $appId
     * @param string $imageType
     * @return view
     */
    public function index(GameImageRequest $request, $appId = null, $imageType = null)
    {
        $selectAppData = $this->gameImageService->getSelectApplicationList();
        $appId = $appId ?: key(array_slice($selectAppData, 0, 1, true));
        $imageType = $imageType ?: config('forms.GameImage.thumbnail');
        $timerList = [];
        $defaultImageList = array('200px' => null, '80px' => null, '60px' => null);
        $timerImageIds = [];
        $appData = [];
        $viewInfoList = [];
        $pwaIconUploaded = false;
        $backgroundColor = '#ffffff';
        $errors =[];

        if ($appId && $selectAppData) {
            $appData = $this->gameImageService->getApplication($appId);

            if (empty($appData->exists)) {
                abort(404);
            }
            $viewInfoList = $this->gameImageService->getViewInfoList($appId);

            if (!array_key_exists($imageType, $viewInfoList)) {
                $imageType = config('forms.GameImage.thumbnail');
            }
        }

        if (!empty($appId)) {
            $timerList = $this->timerService->readTimerByAppId($appId);
            $timerImageIds = [];
            //全部のタイマーで使ってるイメージIdの配列を作る
            foreach ($timerList as $timer) {
                foreach (array_values($timer['imageIdList']) as $imageId) {
                    if (!in_array($imageId, $timerImageIds, true)) {
                        array_push($timerImageIds, $imageId);
                    }
                }
            }
            // 画像サイズ・画像IDごとにタイマーの状態を取得
            $timerStatusAndSizeStringList = $this->timerService->getTimerStatusAndSizeStringList($appId);
            // 定常画像(メイン画像)を取得
            $defaultImageList = $this->defaultImageService->getDefaultImageSizeAndIdByApp($appId);
            // レコメンド候補画像を取得
            $recommendImageList = $this->imageActiveService->getRecommendImageByAppId($appId);
            // 定常画像(メイン画像)が設定されていない場合エラーメッセージを設定
            if (empty($defaultImageList['200px'])) $errors[] = preg_replace('/:attribute/', '200px', trans('validationmessage.MSG317'));
            if (empty($defaultImageList['80px'])) $errors[] = preg_replace('/:attribute/', '80px', trans('validationmessage.MSG317'));
            if (empty($defaultImageList['60px'])) $errors[] = preg_replace('/:attribute/', '60px', trans('validationmessage.MSG317'));
            if (!empty($errors)) $errors[] = trans('validationmessage.MSG318');

            if($request->session()->get('errors')) {
                $sessionErrors[] = $request->session()->get('errors')->all();
                foreach($sessionErrors as $error) {
                    $errors[] = $error[0];
                }
            }
            $pwaIconUploaded = $this->gameImageService->isAllManifestIconUploaded($appId);
            $backgroundColor = $this->pwaManifestService->getBackgroundColor($appId);
        }

        return view('GameImage.index', compact(
            'appId',
            'appData',
            'viewInfoList',
            'selectAppData',
            'imageType',
            'timerList',
            'timerStatusAndSizeStringList',
            'defaultImageList',
            'recommendImageList',
            'timerImageIds',
            'pwaIconUploaded',
            'backgroundColor'
        ))->withErrors(['errors' => $errors]);
    }

    /**
     * 画像詳細
     * @param Request $request
     * @param integer $imgId
     * @return view
     */
    public function show(Request $request, $imgId)
    {
        $imgData = $this->gameImageService->getDetail($imgId);

        if (empty($imgData) || $imgData->exists === false) {
            abort(404);
        }

        return view('GameImage.show', compact(
            'imgData'
        ));
    }

//*********************************************************************************************************************

    /**
     * 掲載設定
     * @param GameImageRequest $request
     */
    public function postStore(GameImageRequest $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->gameImageService->getDetail($imgId);

        if (empty($imgData) || $imgData->exists === false) {
            abort(404);
        }
        try {
            $this->gameImageService->storePostId($imgData);
        } catch (FileUploadException $e) {
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$imgData->app_id, $imgData->image_type]);
    }

    /**
     * 掲載更新
     * @param Request $request
     */
    public function postUpdate(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->gameImageService->getDetail($imgId);

        if (empty($imgData) || $imgData->exists === false) {
            abort(404);
        }

        try {
            $this->gameImageService->updatePostId($imgData);
        } catch (FileUploadException $e) {
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$imgData->app_id, $imgData->image_type]);
    }

    /**
     * 掲載取消
     * @param Request $request
     */
    public function postDestroy(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->gameImageService->getDetail($imgId);

        if (empty($imgData) || $imgData->exists === false) {
            abort(404);
        }
        try {
            $this->gameImageService->destroyPostId($imgData);
        } catch (FileUploadException $e) {
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$imgData->app_id, $imgData->image_type]);
    }

    /**
     * 掲載並び替え
     * @param Request $request
     */
    public function postSort(Request $request)
    {
        $appId = $request->input('app_id');
        $imageType = $request->input('image_type');
        try {
            $this->gameImageService->sortPostId($request->all());
        } catch (FileUploadException $e) {
            // 更新エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$appId, $imageType]);
    }

//*********************************************************************************************************************

    /**
     * 画像削除確認
     * @param Request $request
     * @param integer $imgId
     * @return view
     */
    public function deleteConfirm(Request $request, $imgId)
    {
        $imgData = $this->gameImageService->getDetail($imgId);

        if (empty($imgData) || $imgData->exists === false) {
            abort(404);
        }

        return view('GameImage.deleteconfirm', compact(
            'imgData'
        ));
    }

    /**
     * 画像削除
     * @param Request $request
     * @return view
     */
    public function deleteStore(Request $request)
    {
        $imgId = $request->input('id');
        $imgData = $this->gameImageService->getDetail($imgId);

        if (empty($imgData) || $imgData->exists === false) {
            abort(404);
        }

        $timerListForView = $this->timerService->readTimerByAppId($imgData->app_id);
        // 該当する画像をタイマーが使っている場合、削除できないようにする
        if ($this->timerImageService->validateImageUsingInTimersOnApp($timerListForView, $imgId) == true) {
            $messages = trans('validationmessage.MSG303');
            return back()->withErrors($messages)->withInput($request->all());
        }

        $this->gameImageService->updateSapDelete($imgData);

        return view('GameImage.deletestore');
    }

    /**
     * タイマーを新しく作る
     * requestBodyを加工してserviceに入れる
     * @param ApplicationImageTimerRequest $request
     * @return view, index
     * @throws Exception
     */
    public function createTimer(ApplicationImageTimerRequest $request)
    {
        // フォーマット替え
        $startTime = date('Y-m-d H:i:00', strtotime($request->input('timer-thumbnail-start')));
        $endTime = date('Y-m-d H:i:00', strtotime($request->input('timer-thumbnail-end')));

        $appId = $request->input('app-id');
        $imageType = config('forms.GameImage.thumbnail');

        $imageIds = [
            "200px" => empty($request->input('timer-thumbnail-200px')) ? null : $request->input('timer-thumbnail-200px'),
            "80px" => empty($request->input('timer-thumbnail-80px')) ? null : $request->input('timer-thumbnail-80px'),
            "60px" => empty($request->input('timer-thumbnail-60px')) ? null : $request->input('timer-thumbnail-60px')
        ];

        try {
            $this->timerService->createTimer($appId, $imageIds, $startTime, $endTime);
            // タイマー系のエラーだけcatchする
        } catch (ApplicationImageException $e) {
            // エラー
            $messages[] = $e->getMessage();
            $request->session()->flash('oldRoute', $request->route()->getName());
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$appId, $imageType]);
    }

    /**
     * タイマー更新
     * @param ApplicationImageTimerRequest $request
     * @return bool|\Illuminate\Http\RedirectResponse
     * @throws Exception
     */
    public function updateTimer(ApplicationImageTimerRequest $request)
    {
        // フォーマット替え
        $startTime = date('Y-m-d H:i:00', strtotime($request->input('timer-thumbnail-start')));
        $endTime = date('Y-m-d H:i:00', strtotime($request->input('timer-thumbnail-end')));

        $appId = $request->input('app-id');
        $imageType = config('forms.GameImage.thumbnail');

        $timerId = $request->input('timer-thumbnail-id');

        $imageIds = [
            "200px" => empty($request->input('timer-thumbnail-200px')) ? null : $request->input('timer-thumbnail-200px'),
            "80px" => empty($request->input('timer-thumbnail-80px')) ? null : $request->input('timer-thumbnail-80px'),
            "60px" => empty($request->input('timer-thumbnail-60px')) ? null : $request->input('timer-thumbnail-60px')
        ];

        try {
            $this->timerService->updateTimer($timerId, $appId, $imageIds, $startTime, $endTime);
            // タイマー系のエラーだけcatchする
        } catch (ApplicationImageException $e) {
            // エラー
            $messages[] = $e->getMessage();
            $request->session()->flash('oldRoute', $request->route()->getName());
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$appId, $imageType]);
    }

    /**
     * タイマー削除
     * @param Request $request inputに timerId, appId
     * @return string response
     * @throws Exception
     */
    public function deleteTimer(Request $request)
    {
        $timerId = $request->input('timer-thumbnail-id');
        $appId = $request->input('app-id');

        if (empty($timerId) || empty($appId)) {
            abort(404);
        }

        $this->timerService->deleteTimer($appId, $timerId);

        return $timerId;
    }

    /**
     * メイン画像設定
     * @param Request $request inputにappIdとimageId
     * @return \Illuminate\Http\RedirectResponse
     * @throws Exception
     */
    public function registerDefault(Request $request)
    {
        $appId = $request->input('app-id');
        $imageId = $request->input('id');
        $imageType = config('forms.GameImage.thumbnail');

        if (empty($appId) || empty($imageId)) {
            abort(404);
        }

        try {
            $activeTimerImageList = $this->timerService->getActiveTimerImageList($appId);
            $this->defaultImageService->setDefaultImage($appId, $imageId, $activeTimerImageList);
            // タイマー系のエラーだけcatchする
        } catch (ApplicationImageException $e) {
            // エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$appId, $imageType]);
    }

    /**
     * レコメンド候補画像設定
     * @param Request $request inputにappIdとimageId
     * @return \Illuminate\Http\RedirectResponse
     * @throws Exception
     */
    public function registerRecommend(Request $request)
    {
        $appId = $request->input('app-id');
        $imageId = $request->input('id');
        $imageType = config('forms.GameImage.thumbnail');

        if (empty($appId) || empty($imageId)) {
            abort(404);
        }

        // 定常画像(メイン画像)が設定されていない場合登録せずエラーメッセージを表示させる
        if (!$this->gameImageService->postImageExist($appId, $imageType)) {
            $messages = trans('validationmessage.MSG316');
            return back()->withErrors($messages)->withInput($request->all());
        }

        try {
            // 掲載画像種別管理テーブルにレコメンド候補画像としてレコードを登録
            $this->gameImageService->storeRecommendData($imageId);

        } catch (ApplicationImageException $e) {
            // エラー
            $messages[] = $e->getMessage();
            return back()->withErrors($messages)->withInput($request->all());
        }

        return redirect()->route('GameImage.index', [$appId, $imageType]);
    }

    /**
     * レコメンド候補画像削除
     * @param Request $request
     * @return view, index
     */
    public function deleteRecommend(Request $request)
    {
        $appId = $request->input('app-id');
        $imageId = $request->input('id');
        $imageType = config('forms.GameImage.thumbnail');

        $imgData = $this->gameImageService->getDetail($imageId);
        $this->gameImageService->deactiveRecommendImage($imgData);

        return redirect()->route('GameImage.index', [$appId, $imageType]);
    }

    /**
     * PWA設定
     * @param PwaManifestSettingRequest $request フォームのリクエスト
     * @return \Illuminate\Http\RedirectResponse 成功、失敗ページに帰る。失敗時はエラーメッセージが出る。
     * @throws Exception DBのexceptionが出る
     */
    public function storePwaSetting(PwaManifestSettingRequest $request)
    {
        $appId = $request->input('app-id');
        $imageType = config('forms.GameImage.manifestIcon');
        $backgroundColor = $request->input('background_color');

        if (empty($appId)) {
            abort(404);
        }

        // manifest iconが全部アップロードされてないとエラー
        if ($this->gameImageService->isAllManifestIconUploaded($appId) === false) {
            return back()->withErrors(trans('validationmessage.MSG313'))->withInput($request->all());
        }

        $this->pwaManifestService->setBackgroundColor($appId, $backgroundColor);

        return redirect()->route('GameImage.index', [$appId, $imageType]);
    }
}
