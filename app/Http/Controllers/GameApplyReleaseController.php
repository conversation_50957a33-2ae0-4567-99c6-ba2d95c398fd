<?php
namespace App\Http\Controllers;

use Exception;
use Log;
use Illuminate\Http\JsonResponse;
use App\Libs\Apply\ApplyCommon;
use App\Http\Requests\ApplyReleaseRequest;
use App\Services\ApplyReleaseService;
use App\Services\ApplyNotificationService;

/**
 * リリース申請コントローラー
 */
class GameApplyReleaseController extends Controller {

    /** @var ApplyReleaseService */
    protected $applyReleaseService;

    /** @var ApplyNotificationService */
    protected $applyNotificationService;

    /** 通知メールに記載されるURLに指定するtagパラメータの値 */
    private $tag = 'release';

    public function __construct(
        ApplyReleaseService $applyReleaseService,
        ApplyNotificationService $applyNotificationService)
    {
        parent::__construct();
        $this->applyReleaseService = $applyReleaseService;
        $this->applyNotificationService = $applyNotificationService;
    }
    
    /**
     * 審査用の画像を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeExaminationImages(ApplyReleaseRequest $request, $id, $device)
    {
        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyExaminationImages($id, $device);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'examinationImages';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
    
    /**
     * ゲーム紹介ページ：デザイン部分素材を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @param  mixed $applyType
     * @return void
     */
    public function storeIntroductionImages(ApplyReleaseRequest $request, $id, $device){
        $catchphraseImage = $request->input('catchphrase_image');
        $catchphraseImageText = $request->input('catchphrase_image_text');
        $characterPriorityNotes = $request->input('character_priority_notes');
        $characterPriorityNotesText = $request->input('character_priority_notes_text');
        $copyright = $request->input('copyright');
        $copyrightText = $request->input('copyright_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyIntroductionImages(
                $id, $device, 
                $catchphraseImage, $catchphraseImageText,
                $characterPriorityNotes, $characterPriorityNotesText,
                $copyright, $copyrightText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'introductionImages';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * プラットフォーム上に掲載される画像を審査に提出
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return JsonResponse
     */
    public function storePlatformImages(ApplyReleaseRequest $request, $id, $device){
        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $this->applyReleaseService->applyPlatformImages($id, $device);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'platformImages';
        $this->registApplyNotification($id, $device, $categoryType);

         return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
    
    /**
     * 公式サイト検証を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeReleasesite(ApplyReleaseRequest $request, $id, $device){
        $isFollowingTermsCreate = boolval($request->input('is_following_terms_create'));
        $meansVerificationText = $request->input('means_verification_text');
        $officialSiteUrlText = $request->input('official_site_url_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyReleasesite(
                $id, $device,
                $isFollowingTermsCreate, $meansVerificationText, $officialSiteUrlText);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'releaseSite';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
    
    /**
     * コミュニティを審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeCommunity(ApplyReleaseRequest $request, $id, $device){

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyCommunity($id, $device);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'community';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * リリース申請のゲーム情報入力を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @param  mixed $applyType
     * @return void
     */
    public function storeGameInformation(ApplyReleaseRequest $request, $id, $device){
        $recommendationAgeDivision = $request->input('recommendation_age_division');
        $gameFormalName = $request->input('game_formal_name_text');
        $taxIncludedPrice = $request->input('tax_included_price_value');
        $taxExcludedPrice = $request->input('tax_excluded_price_value');
        $clientGameIntroduction = $request->input('client_game_introduction_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyGameInformation(
                $id, $device,
                $recommendationAgeDivision, $gameFormalName, $taxIncludedPrice, $taxExcludedPrice, $clientGameIntroduction
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'gameInformation';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }
  
    /**
     * 動作検証を審査に提出：リリース申請
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     */
    public function storeVerification(ApplyReleaseRequest $request, $id, $device){
        $apkUpload = $request->file('apk_upload');
        $isBugCheckDone = boolval($request->input('is_bug_check_done'));
        $isGameTestingDone = boolval($request->input('is_game_testing_done'));
        $isFinishedProduct = boolval($request->input('is_finished_product'));
        $isExaminationCheckDone = boolval($request->input('is_examination_check_done'));
        $gameStartUrl = $request->input('game_start_url');
        $isOtherWorksUsedAndUnMoralityExpressionUnUsed = boolval($request->input('is_other_works_used_and_un_morality_expression_un_used'));
        $isFollowingTermsDevelop = boolval($request->input('is_following_terms_develop'));
        $isFollowingTermsManagement = boolval($request->input('is_following_terms_management'));
        $inGameVirtualCurrency = $this->applyReleaseService->convertBool($request->input('in_game_virtual_currency'));
        $isFollowingTermsOfService = $this->applyReleaseService->convertBool($request->input('is_following_terms_of_service'));
        $usingInspectionApi = $this->applyReleaseService->convertBool($request->input('using_inspection_api'));
        $inspectionApiVerificationMethod = $request->input('inspection_api_verification_method');
        $tagsToSetInGame = $request->input('tags_to_set_in_game');
        $isSignedByDigitalSignature = boolval($request->input('is_signed_by_digital_signature'));
        $isUsingExternalInstaller = $this->applyReleaseService->convertBool($request->input('is_using_external_installer'));
        $isUsingModule = $this->applyReleaseService->convertBool($request->input('is_using_module'));
        $hasTradeFeatureInGame = $this->applyReleaseService->convertBool($request->input('has_trade_feature_in_game'));
        $hasGamechip = $this->applyReleaseService->convertBool($request->input('has_gamechip'));
        $ngwordVersionText = $request->input('ngword_version_text');
        $hasNgwordCheck = boolval($request->input('has_ngword_check'));


        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyVerification(
                $id, $device,
                $apkUpload, $isBugCheckDone, $isGameTestingDone, $isFinishedProduct, $isExaminationCheckDone, $gameStartUrl,
                $isOtherWorksUsedAndUnMoralityExpressionUnUsed,
                $isFollowingTermsDevelop, $isFollowingTermsManagement,
                $inGameVirtualCurrency, $isFollowingTermsOfService, $usingInspectionApi, $inspectionApiVerificationMethod, 
                $tagsToSetInGame, $isSignedByDigitalSignature, $isUsingExternalInstaller, $isUsingModule,
                $hasTradeFeatureInGame, $hasGamechip, $ngwordVersionText, $hasNgwordCheck
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'verification';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * Linksmateを審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeLinksmate(ApplyReleaseRequest $request, $id, $device){
        $linksmateCopyright = $request->input('linksmate_copyright');
        $linksmateCopyrightText = $request->input('linksmate_copyright_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyLinksmate(
                $id, $device, 
                $linksmateCopyright, $linksmateCopyrightText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'linksmate';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * Win対応環境を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeWindowsSupportedEnvironment(ApplyReleaseRequest $request, $id, $device){
        $osVersionText = $request->input('os_version_text');
        $processorText = $request->input('processor_text');
        $memorySize = $request->input('memory_size');
        $memorySizeUnit = $request->input('memory_size_unit');
        $graphicsText = $request->input('graphics_text');
        $capacitySize = $request->input('capacity_size');
        $capacitySizeUnit = $request->input('capacity_size_unit');
        $noteText = $request->input('note_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyWindowsSupportedEnvironment(
                $id, $device,
                $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'windowsSupportedEnvironment';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * Mac対応環境を審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeMacSupportedEnvironment(ApplyReleaseRequest $request, $id, $device){
        $osVersionText = $request->input('os_version_text');
        $processorText = $request->input('processor_text');
        $memorySize = $request->input('memory_size');
        $memorySizeUnit = $request->input('memory_size_unit');
        $graphicsText = $request->input('graphics_text');
        $capacitySize = $request->input('capacity_size');
        $capacitySizeUnit = $request->input('capacity_size_unit');
        $noteText = $request->input('note_text');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyMacSupportedEnvironment(
                $id, $device,
                $osVersionText, $processorText, $memorySize, $memorySizeUnit, $graphicsText, $capacitySize, $capacitySizeUnit, $noteText
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'macSupportedEnvironment';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * CEROを審査に提出する
     *
     * @param  mixed $request
     * @param  mixed $id
     * @param  mixed $device
     * @return void
     */
    public function storeCero(ApplyReleaseRequest $request, $id, $device){
        $classificationText = $request->input('classification_text');
        $contentIcons = $request->input('content_icons');

        try {
            if (!$this->isDeveloperApplication($id, $device)) {
                abort(400);
            }

            $result = $this->applyReleaseService->applyCero(
                $id, $device,
                $classificationText, $contentIcons
            );
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('id', 'device')),
                500
            );
        }

        // メール送信
        $categoryType = 'cero';
        $this->registApplyNotification($id, $device, $categoryType);

        return response()->json([
            'status' => 200,
            'message' => "success"
        ],200);
    }

    /**
     * PF運営宛の審査提出の通知メールを送信する
     *
     * @param  mixed $id
     * @param  mixed $device
     * @param  mixed $categoryType
     * @return void
     */
    private function registApplyNotification($id, $device, $categoryType){
        // メール送信
        try{
            $this->applyNotificationService->registApplyNotification($id, $device,
                $this->tag, $categoryType);
        } catch (Exception $e) {
            // 未知のエラー
            Log::error(sprintf("PF運営宛のメール通知に失敗しました。appId:%s device:%s category:%s ExceptionMessage:%s\n%s",
                $id, $device, $categoryType, $e->getMessage(), $e->getTraceAsString()));
        }
    }

    /**
     * デベロッパーの対象アプリかを判定する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return bool
     */
    public function isDeveloperApplication($appId, $device){
        return $this->applyNotificationService->isDeveloperApplication($appId, $device);
    }
}
