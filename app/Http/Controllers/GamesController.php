<?php
namespace App\Http\Controllers;

use Exception;
use Log;
use Illuminate\Http\Request;
use App\Libs\Apply\ApplyCommon;
use App\Services\GamesService;
use App\Services\ApplyPreregistrationService;
use App\Services\ApplyReleaseService;
use App\Services\ApplyService;
use App\Services\BoardService;
use App\Services\ApplyNotificationService;
use App\Http\Requests\Games\BasicRequest;
use App\Http\Requests\Games\DevicePcRequest;
use App\Http\Requests\Games\DeviceSpRequest;
use App\Http\Requests\Games\DeviceMobileRequest;
use App\Http\Requests\Games\DeviceAndroidAppRequest;
use App\Http\Requests\Games\DeviceApkCloudRequest;
use App\Http\Requests\Games\DeviceEmulatorRequest;
use App\Http\Requests\Games\ApkRequest;
use App\Http\Requests\Games\EmulatorApkRequest;
use App\Http\Requests\Games\EmulatorKeyMappingRequest;
use App\Http\Requests\Games\EmulatorKeyMappingFileRequest;
use App\Http\Requests\Games\LatestRequest;
use App\Http\Requests\Games\TitleGroupRequest;

/**
 * ゲーム登録情報
 */
class GamesController extends Controller
{
    /** @var GamesService */
    protected $gamesService;
    
    /** @var ApplyPreregistrationService */
    protected $applyPreregistrationService;

    /** @var ApplyReleaseService */
    protected $applyReleaseService;

    /** @var ApplyService */
    protected $applyService;

    /** @var BoardService */
    protected $boardService;

    /** @var ApplyNotificationService */
    protected $applyNotificationService;
    
    /**
     * GamesController constructor.
     *
     * @param GamesService $gamesService
     */
    public function __construct(
        GamesService $gamesService,
        ApplyPreregistrationService $applyPreregistrationService,
        ApplyReleaseService $applyReleaseService,
        ApplyService $applyService,
        BoardService $boardService,
        ApplyNotificationService $applyNotificationService)
    {
        parent::__construct();
        $this->gamesService = $gamesService;
        $this->applyPreregistrationService = $applyPreregistrationService;
        $this->applyReleaseService = $applyReleaseService;
        $this->applyService = $applyService;
        $this->boardService = $boardService;
        $this->applyNotificationService = $applyNotificationService;
        view()->share($this->gamesService->getFormData($this->getActionName()));

        // JS読み込み
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/controller/Games/Common.js';
        $javascriptFileList[] = '/js/apply/games.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);

        // 404
        if (! auth_is_sap()) {
            $action = $this->getActionName();
            $allowedActions = ['index', 'applicationToken.store', 'applicationToken.delete', 
                'board', 'board.comment',
                'notification.update', 'notification.update.completed'
            ];
            if (!in_array($action, $allowedActions)) {
                abort(404);
            }
        }
    }

    /**
     * 一覧
     *
     * @param Request $request
     * @return view
     */
    public function index(Request $request)
    {
        $titleGroupConfigs = config('forms.ApplicationTitleGroup');

        $condition = $this->gamesService->formatSearchCondition($request->all());

        if (auth_is_pf() && count($condition) == 0) {
            // PFアカウントの場合は全件を表示します
            $paginator = $this->gamesService->getListAll();
            return view('Games.simplelist', compact('paginator'));
        } else {
            $app_id = null;
            $titleGroup = [];
            $isDisplayTitleGroup = false;
            $paginator = $this->gamesService->getList($condition);
            $pagerView = $this->gamesService->getPagerView($paginator, config('forms.Games.pagerLinkNum'));
            $tab = $request->get('tab', config('forms.Games.defaultTab'));

            // フロント用アプリケーションID取得
            foreach($paginator as $data){
                $app_id = $data->id;
                // ついでにタイトルグループ設定の表示対象判定
                if ($data->general && in_array($data->division, $titleGroupConfigs['targetDivision'])) {
                    $isDisplayTitleGroup = true;
                }
            }

            // トークン一覧と公開鍵取得
            if ($app_id) {
                $applicationToken = $this->gamesService->getReciboTokenList($app_id);
                $emulatorToken = $applicationToken['emulator'];
                $androidAppToken = $applicationToken['android_app'];
                $pcToken = $applicationToken['pc'];
                $spToken = $applicationToken['sp'];
            }

            // 表示対象の場合はタイトルグループ情報取得
            if ($isDisplayTitleGroup) {
                $titleGroup = $this->gamesService->getTitleGroup($app_id);
            }

            // お知らせの情報取得
            $informationTopics = $this->boardService->getInformationTopics($app_id, 'application');

            // 事前登録及びリリース申請登録フォームの情報取得
            $device = $request->get('device');
            // デバイスが未設定の時にデフォルトデバイスを設定する
            if(empty($device)){
                $data = $paginator->first(); // 本機能のページングはページ毎のアイテム数は１の仕様ととなっているので、最初の１件目を参照すれば良い
                if(!empty($data->device->pc->exists)){
                    $device = 'pc';
                }
                elseif(!empty($data->device->sp->exists)){
                    $device = 'sp';
                }
                elseif(!empty($data->device->mobile->exists)){
                    $device = 'mobile';
                }
                elseif(!empty($data->device->android_app->exists)){
                    $device = 'android_app';
                }
                elseif(!empty($data->device->emulator->exists)){
                    $device = 'emulator';
                }
            }
            $preregistrationEnable = $this->applyPreregistrationService->isTargetDevice($device);
            $releaseEnable = $this->applyReleaseService->isTargetDevice($device);
            $preregistration = null;
            $release = null;
            $applyCommonInfomation = null;

            $informationTopics = null;
            $notificationAddress = $this->applyNotificationService->getNotificationAddress($app_id, 'application');

            if($tab == 'preregistration' || $tab == 'release'){
                // お知らせの情報取得
                $informationTopics = $this->boardService->getInformationTopics($app_id, 'application');
                // 通知メールアドレス取得
                $notificationAddress = $this->applyNotificationService->getNotificationAddress($app_id, 'application');

                switch ($tab) {
                    case 'preregistration': // 事前登録タブが選択されている場合
                        if ($preregistrationEnable) {
                            $preregistration = $this->applyPreregistrationService->getPreregistration($app_id, $request->get('device', $device));
                            $applyCommonInfomation = $preregistration;
                        } else {
                            $tab = 'basic';
                        }
                        break;
                    case 'release': // リリース申請タブが選択されている場合
                        if ($releaseEnable) {
                            $release = $this->applyReleaseService->getRelease($app_id, $request->get('device', $device));
                            // 事前登録が完了している場合は、事前登録情報クラスをリリース申請情報クラスに設定する
                            if($release->isPreRegistrationActive()){
                                $preregistration = $this->applyPreregistrationService->getPreregistration($app_id, $request->get('device', $device));
                                $release->setPreregistration($preregistration);
                            }
                            $applyCommonInfomation = $release;
                        } else {
                            $tab = 'basic';
                        }
                        break;
                    default:
                        break;
                }
            }

            return view(
                'Games.index',
                compact(
                    'paginator',
                    'pagerView',
                    'emulatorToken',
                    'pcToken',
                    'spToken',
                    'androidAppToken',
                    'isDisplayTitleGroup',
                    'titleGroup',
                    'tab',
                    'device',
                    'preregistrationEnable',
                    'releaseEnable',
                    'preregistration',
                    'release',
                    'applyCommonInfomation',
                    'informationTopics',
                    'notificationAddress'
                )
            );
        }
    }

    public function basicEdit($id)
    {
        $data = $this->gamesService->getBasic($id);
        if (empty($data->exists)) {
            abort(404);
        }
        return view('Games.basicedit', compact('data'));
    }

    public function basicEditConfirm(BasicRequest $request)
    {
        return view('Games.basiceditconfirm');
    }

    public function basicUpdate(BasicRequest $request)
    {
        $this->gamesService->basicUpdate($request->all());
        $subScreenName = config('forms.Games.subScreenName.basic');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    public function devicePcEdit($id)
    {
        $data = $this->gamesService->getDevicePc($id);
        if (empty($data->exists)) {
            abort(404);
        }
        $isEnableDomainType = $this->gamesService->isEnableDomainType($id, 'pc');
        return view('Games.devicepcedit', compact('data', 'isEnableDomainType'));
    }

    public function devicePcEditConfirm(DevicePcRequest $request)
    {
        $isEnableDomainType = $this->gamesService->isEnableDomainType($request->get('id'), 'pc');
        return view('Games.devicepceditconfirm', compact('isEnableDomainType'));
    }

    public function devicePcUpdate(DevicePcRequest $request)
    {
        $this->gamesService->devicePcUpdate($request->all());
        $subScreenName = config('forms.Games.subScreenName.pc');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    public function deviceSpEdit($id)
    {
        $data = $this->gamesService->getDeviceSp($id);
        if (empty($data->exists)) {
            abort(404);
        }
        $isEnableDomainType = $this->gamesService->isEnableDomainType($id, 'sp');
        return view('Games.devicespedit', compact('data', 'isEnableDomainType'));
    }

    public function deviceSpEditConfirm(DeviceSpRequest $request)
    {
        $isEnableDomainType = $this->gamesService->isEnableDomainType($request->get('id'), 'sp');
        return view('Games.devicespeditconfirm', compact('isEnableDomainType'));
    }

    public function deviceSpUpdate(DeviceSpRequest $request)
    {
        $this->gamesService->deviceSpUpdate($request->all());
        $subScreenName = config('forms.Games.subScreenName.sp');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    public function deviceMobileEdit($id)
    {
        $data = $this->gamesService->getDeviceMobile($id);
        if (empty($data->exists)) {
            abort(404);
        }
        return view('Games.devicemobileedit', compact('data'));
    }

    public function deviceMobileEditConfirm(DeviceMobileRequest $request)
    {
        return view('Games.devicemobileeditconfirm');
    }

    public function deviceMobileUpdate(DeviceMobileRequest $request)
    {
        $this->gamesService->deviceMobileUpdate($request->all());
        $subScreenName = config('forms.Games.subScreenName.mobile');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    public function deviceAndroidAppEdit($id)
    {
        $data = $this->gamesService->getDeviceAndroidApp($id);
        if (empty($data->exists)) {
            abort(404);
        }
        return view('Games.deviceandroidappedit', compact('data'));
    }

    public function deviceAndroidAppEditConfirm(DeviceAndroidAppRequest $request)
    {
        return view('Games.deviceandroidappeditconfirm');
    }

    public function deviceAndroidAppUpdate(DeviceAndroidAppRequest $request)
    {
        $this->gamesService->deviceAndroidAppUpdate($request->all());
        $subScreenName = config('forms.Games.subScreenName.android_app');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    /**
     * Device APK Cloud Edit
     */

    public function deviceApkCloudEdit($id)
    {
        $data = $this->gamesService->getDeviceApkCloud($id);
        if (empty($data->exists)) {
            abort(404);
        }
        $vendor = config('forms.Games.vendor');
        return view('Games.deviceapkcloudedit', compact('data', 'vendor'));
    }

    public function deviceApkCloudEditConfirm(DeviceApkCloudRequest $request)
    {
        $vendor = config('forms.Games.vendor');
        return view('Games.deviceapkcloudeditconfirm', compact('vendor'));
    }

    public function deviceApkCloudUpdate(DeviceApkCloudRequest $request)
    {
        $this->gamesService->deviceApkCloudUpdate($request->all());
        $subScreenName = config('forms.Games.subScreenName.apk_cloud');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    /**
     * Device Emulator Edit
     */

    public function deviceEmulatorEdit($id)
    {
        $data = $this->gamesService->getDeviceEmulator($id);
        if (empty($data->exists)) {
            abort(404);
        }
        return view('Games.deviceemulatoredit', compact('data'));
    }

    public function deviceEmulatorEditConfirm(DeviceEmulatorRequest $request)
    {
        return view('Games.deviceemulatoreditconfirm');
    }

    public function deviceEmulatorUpdate(DeviceEmulatorRequest $request)
    {
        $this->gamesService->deviceEmulatorUpdate($request->all());
        $subScreenName = config('forms.Games.subScreenName.emulator');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    /**
     * @param int $id アプリID
     */
    public function apkEdit($id)
    {
        $data = $this->gamesService->getApk($id);
        if (empty($data->exists)) {
            abort(404);
        }
        $data->file_timestamp = $this->gamesService->getAPKFileTimestamp($data['apk_name']);
        return view('Games.apkedit', compact('data'));
    }

    public function apkEditConfirm(ApkRequest $request)
    {
        $current = $this->gamesService->getApk($request->get('id'));
        if (empty($current->exists)) {
            abort(404);
        }
        $current->file_timestamp = $this->gamesService->getAPKFileTimestamp($current['apk_name']);

        $apk = $request->file('apk_file');

        $up = $this->gamesService->getUploadApkInfo($apk);

        // パッケージ名、バージョンコード、書名の確認
        $errors = $this->gamesService->apkUpdateValidation($up, $current);
        if (count($errors) > 0) {
            array_unshift($errors, ['file_failure' => trans('validationmessage.MSG324')]);
        } else {
            // APKの一時保存
            $errors = array_merge($errors,$this->gamesService->apkFileTmpSave($apk));
        }
        if ($errors) {
            return view('Games.apkeditconfirm', compact('current', 'up'))->withErrors($errors);
        }

        $request->session()->put('apk_info', json_decode(json_encode($up), true));

        return view('Games.apkeditconfirm', compact('current', 'up'));
    }

    public function apkUpdate(Request $request)
    {
        $subScreenName = config('forms.Games.subScreenName.apk');
        $actionName = '編集';
        $apkInfo = $request->session()->get('apk_info');
        $request->session()->forget('apk_info');
        $view = view('Games.store', compact('subScreenName', 'actionName', 'apkInfo'));

        // APK情報更新・APKファイル更新
        $apkInfo['id'] = $request->get('id');
        $result = $this->gamesService->apkUpdate($apkInfo);
        if (is_array($result)) {
            if (array_key_exists('message', $result)) {
                return $view->withErrors($result);
            } else {
                return redirect()
                ->route('Games.apk.edit', ['id' => $request->get('id')])
                ->withInput()
                ->withErrors($result);
            }
        } else if (!$result) {
            return redirect()
            ->route('Games.apk.edit', ['id' => $request->get('id')])
            ->withInput()
            ->withErrors(['apk_file' => 'APK情報の更新に失敗しました。']);
        }

        return $view;
    }

    public function emulatorApkEdit($id)
    {
        $data = $this->gamesService->getEmulatorApk($id);
        if (empty($data->exists)) {
            abort(404);
        }
        $data->file_timestamp = $this->gamesService->getAPKFileTimestamp($data['apk_name']);
        return view('Games.emulatorapkedit', compact('data'));
    }

    public function emulatorApkEditConfirm(EmulatorApkRequest $request)
    {
        $current = $this->gamesService->getEmulatorApk($request->get('id'));
        if (empty($current->exists)) {
            abort(404);
        }
        $current->file_timestamp = $this->gamesService->getAPKFileTimestamp($current['apk_name']);

        $apk = $request->file('apk_file');

        $up = $this->gamesService->getUploadEmulatorApkInfo($apk);
        $up->url = $request->get('url');

        // パッケージ名、バージョンコード、書名の確認
        $errors = $this->gamesService->apkUpdateValidation($up, $current);
        if (count($errors) > 0) {
            array_unshift($errors, ['file_failure' => trans('validationmessage.MSG324')]);
        } else {
            // APKの一時保存
            $errors = array_merge($errors,$this->gamesService->emulatorApkFileTmpSave($apk));
        }
        if ($errors) {
            return view('Games.emulatorapkeditconfirm', compact('current', 'up'))->withErrors($errors);
        }
        $request->session()->put('emulator_apk_info', json_decode(json_encode($up), true));

        return view('Games.emulatorapkeditconfirm', compact('current', 'up'));
    }

    public function emulatorApkUpdate(Request $request)
    {
        $subScreenName = config('forms.Games.subScreenName.emulatorapk');
        $actionName = '編集';
        $view = view('Games.store', compact('subScreenName', 'actionName'));

        $apkInfo = $request->session()->get('emulator_apk_info');
        $request->session()->forget('emulator_apk_info');

        // EmulatorAPK情報更新・EmulatorAPKファイル更新
        $apkInfo['id'] = $request->get('id');
        $result = $this->gamesService->emulatorApkUpdate($apkInfo);
        if (is_array($result)) {
            if (array_key_exists('message', $result)) {
                return $view->withErrors($result);
            } else {
                return redirect()
                ->route('Games.emulatorapk.edit', ['id' => $request->get('id')])
                ->withInput()
                ->withErrors($result);
            }
        } else if (!$result) {
            return redirect()
            ->route('Games.emulatorapk.edit', ['id' => $request->get('id')])
            ->withInput()
            ->withErrors(['apk_file' => 'APK情報の更新に失敗しました。']);
        }

        return $view;
    }

    public function latestList(Request $request, $id)
    {
        $this->gamesService->formatSearchConditionForLatest($request->all());

        $appTitleType = $this->gamesService->getAppTitleType();
        if (! isset($appTitleType[$id])) {
            abort(404);
        }
        $condition = $request->all();
        $condition['app_id'] = $id;
        $paginator = $this->gamesService->getLatestList($condition);
        $pagerView = $this->gamesService->getPagerView($paginator, config('forms.Games.latest.pagerLinkNum'));
        return view('Games.latestlist', compact('paginator', 'pagerView', 'id'));
    }

    public function latestCreate($id)
    {
        $appTitleType = $this->gamesService->getAppTitleType();
        if (! isset($appTitleType[$id])) {
            abort(404);
        }
        return view('Games.latestcreate', compact('id'));
    }

    public function latestCreateConfirm(LatestRequest $request)
    {
        return view('Games.latestcreateconfirm');
    }

    public function latestStore(LatestRequest $request)
    {
        $this->gamesService->latestStore($request->all());
        $actionName = '登録';

        return view('Games.lateststore', compact('actionName'));
    }

    public function latestEdit($id)
    {
        $data = $this->gamesService->getLatest($id);
        if (empty($data->exists)) {
            abort(404);
        }
        return view('Games.latestedit', compact('data'));
    }

    public function latestEditConfirm(LatestRequest $request)
    {
        return view('Games.latesteditconfirm');
    }

    public function latestUpdate(LatestRequest $request)
    {
        $this->gamesService->latestUpdate($request->all());
        $actionName = '編集';
        return view('Games.lateststore', compact('actionName'));
    }

    public function latestDestroy(Request $request)
    {
        $this->gamesService->latestDestroy($request->all());
        $message = $this->gamesService->getMessage('latest.afterDelete');
        $request->session()->flash('message', $message);
        if ($request->has('app_id')) {
            return redirect()->route('Games.latest.list', [
                'id' => $request->get('app_id')
            ]);
        }
        return redirect()->route('Games.index', [
            'search' => 'on'
        ]);
    }

    /**
     * Emulatorキーマッピング変更画面
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function emulatorKeyMappingEdit($id)
    {
        $data = $this->gamesService->getEmulatorKeyMapping($id);
        if (empty($data->exists)) {
            abort(404);
        }
        return view('Games.emulatorkeymappingedit', compact('data'));
    }

    /**
     * Emulatorキーマッピング変更確認画面
     *
     * @param EmulatorKeyMappingRequest $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function emulatorKeyMappingEditConfirm(EmulatorKeyMappingRequest $request)
    {
        $data = $this->gamesService->getEmulatorKeyMapping($request->get('id'));
        if (empty($data->exists)) {
            abort(404);
        }
        return view('Games.emulatorkeymappingeditconfirm', compact('data'));
    }

    /**
     * Emulatorキーマッピング変更完了画面
     *
     * @param EmulatorKeyMappingRequest $request
     * @param EmulatorKeyMappingFileRequest $fileRequest
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function emulatorKeyMappingUpdate(
        EmulatorKeyMappingRequest $request,
        EmulatorKeyMappingFileRequest $fileRequest
    ) {
        $subScreenName = config('forms.Games.subScreenName.emulatorkeymapping');
        $actionName = '変更';
        $view = view('Games.store', compact('subScreenName', 'actionName'));

        // Emulatorキーマッピング情報更新・Emulatorキーマッピング設定ファイル更新
        $result = $this->gamesService->emulatorKeyMappingUpdate($fileRequest->all());
        if (!$result || is_array($result)) {
            if (isset($result['message'])) {
                return $view->withErrors($result);
            } else {
                if (!$result) {
                    // エラーメッセージをセット
                    $result = ['key_mapping_file' => 'キーマッピング情報の更新に失敗しました。'];
                }

                return redirect()
                    ->route('Games.emulatorkeymapping.edit', ['id' => $request->get('id')])
                    ->withInput()
                    ->withErrors($result);
            }
        }

        return $view;
    }

    /**
     * トークン新規作成
     *
     * @param Request $request
     * @return view
     */
    public function storeApplicationToken(Request $request)
    {
        // 初期化
        $result = [];

        // フロント用アプリケーションID取得
        $app_id = $request->get('app_id');
        // レシート用アプリケーションID取得
        $receiptApplicationId = $request->get('receiptApplicationId');

        // レシート用アプリケーションIDからトークンを新規作成
        if ($receiptApplicationId) {
            $result = $this->gamesService->storeApplicationToken($receiptApplicationId);

            if (!$result['resultStatus']) {
                // アプリケーションAPIトークンの追加APIが落ちた時
                return view('ReceiptApi.receiptApiError');
            }
        } else {
            // アプリケーション更新（追加）APIが落ちた時
            return view('ReceiptApi.receiptApiError');
        }

        // リダイレクトする
        return redirect()->route('Games.index', ['app_id' => $app_id]);
    }

    /**
     * トークン削除
     *
     * @param Request $request
     * @return view
     */
    public function deleteApplicationToken(Request $request)
    {
        // 初期化
        $result = [];

        // フロント用アプリケーションID取得
        $app_id = $request->get('app_id');

        // トークンID取得
        $applicationTokenId = $request->get('applicationTokenId');

        // トークンIDからトークンを削除
        if ($applicationTokenId) {
            $result = $this->gamesService->deleteApplicationToken($applicationTokenId);

            if (!$result['resultStatus']) {
                // アプリケーションAPIトークンの削除APIが落ちた時
                return view('ReceiptApi.receiptApiError');
            }
        } else {
            // アプリケーション更新（追加）APIが落ちた時
            return view('ReceiptApi.receiptApiError');
        }

        // リダイレクトする
        return redirect()->route('Games.index', ['app_id' => $app_id]);
    }

    /**
     * タイトルグループ編集
     *
     * @param $appID
     * @return view
     */
    public function titleGroupEdit($appID)
    {
        // タイトルグループ情報を取得
        $titleGroup = $this->gamesService->getTitleGroup($appID);

        return view('Games.titlegroupedit', compact('appID', 'titleGroup'));
    }

    /**
     * タイトルグループ編集確認
     *
     * @param TitleGroupRequest $request
     * @return view
     */
    public function titleGroupEditConfirm(TitleGroupRequest $request)
    {
        return view('Games.titlegroupeditconfirm');
    }

    /**
     * タイトルグループ編集完了
     *
     * @param TitleGroupRequest $request
     * @return view
     */
    public function titleGroupUpdate(TitleGroupRequest $request)
    {
        $result = $this->gamesService->titleGroupUpdate($request->all());
        if ($result === false) {
            abort(400);
        }

        $subScreenName = config('forms.Games.subScreenName.titleGroup');
        $actionName = '編集';
        return view('Games.store', compact('subScreenName', 'actionName'));
    }

    /**
     * 画像・ファイル追加モーダル表示
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return \Illuminate\View\View
     */
    public function board(Request $request, $appId)
    {
        if (!$this->isDeveloperApplication($appId)) {
            abort(404);
        }

        // これがないと掲示板表示時にJSでエラーが発生し、JS処理が実行できなくなるので追加
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/readmore.min.js';
        $javascriptFileList[] = '/js/apply/board.js';
        $javascriptFileList[] = '/js/apply/file.js';
        view()->share(['javascriptFileList' => $javascriptFileList]);

        $page = $request->get('page', 1);
        $perPageNum = $request->get('perPage', 10);

        // コメントの取得
        $kind = 'application';
        $paginator = $this->boardService->getMessageBoardContent($request, $appId, $kind, $page, $perPageNum);

        // messageNumberが指定されている場合は該当ページにリダイレクト
        // MEMO: メッセージ削除機能がないことを前提にページ番号を計算しているため削除機能追加時は注意すること
        $messageNumber = $request->get('messageNumber');
        if (!is_null($messageNumber) && is_numeric($messageNumber)) {
            $page = (int) ceil(($paginator->total() - ($messageNumber - 1))  / $perPageNum);
            if ($page > 0) {
                return redirect()->route('Games.board', ['id' => $appId, 'page' => $page]);
            }
        }

        $pagerView = $this->boardService->getPagerView($paginator, config('forms.Games.pagerLinkNum'));
        $baseRoute = 'Games';

        $response = $this->applyService->getTargetInformation($appId, $kind);
        $releaseStatus = !empty($response['releaseStatus']) ? $response['releaseStatus']: '';

        $appTitleType = $this->gamesService->getAppTitleType();
        $appTitle = $appTitleType[$appId];

        return view('Games.modals.board', compact(
            'appId',
            'paginator',
            'pagerView',
            'baseRoute',
            'appTitle',
            'releaseStatus'
        ));
    }

    /**
     * ファイルアップロード用署名付きURL発行
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return json
     */
    public function generateSignedUrl(Request $request, $appId)
    {
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }
    
            $uploadFileNum = $request->get('uploadFileNum');
            // ファイルアップロード用の署名付きURLの発行
            $response = $this->boardService->generateSignedUrls($appId, config('forms.Games.boardFileUploadDirectory'), $uploadFileNum);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * 掲示板コメント追加
     * @param Request $request リクエスト
     * @param integer $appId アプリID
     * @return json
     */
    public function addBoardComment(Request $request, $appId)
    {
        // 掲示板のコメント追加処理
        try {
            if (!$this->isDeveloperApplication($appId)) {
                abort(400);
            }

            $content = $request->get('comment');
            $uploadFileList = $request->get('uploadFileList');
            $directory = config('forms.Games.boardFileUploadDirectory');
            $response = $this->boardService->postMessageContent(
                $appId,
                'application',
                $content,
                $uploadFileList,
                $directory
            );

            // メール送信
            $this->registPostCommentSapNotification($appId);

            return response()->json([
                'status' => $response['status'],
                'message' => $response['message'],
                "data" => $response['body']
            ], $response['status']);
        } catch (Exception $e) {
            // 未知のエラー
            return response()->json(
                ApplyCommon::getJsonError($e, $request, compact('appId')),
                500
            );
        }
    }

    /**
     * 通知先更新
     *
     * @param  mixed $request
     * @param  mixed $appId
     * @param  mixed $device
     * @return void
     */
    public function updateNotification(Request $request, $appId, $device){
        if (!$this->isDeveloperApplication($appId, $device)) {
            abort(404);
        }

        // これがないと掲示板表示時にJSでエラーが発生し、JS処理が実行できなくなるので追加
        $javascriptFileList = view()->shared('javascriptFileList');
        $javascriptFileList[] = '/js/readmore.min.js';
        
        return view('Games.modals.notification.update', compact(
            'javascriptFileList', 'appId', 'device'
        ));
    }

    /**
     * PF運営宛の掲示板コメント追加の通知メールを送信する
     *
     * @param mixed $id
     * @return void
     */
    private function registPostCommentSapNotification($id){
        // メール送信
        $kind = 'application';
        try{
            $this->applyNotificationService->registPostCommentSapNotification($id, $kind);
        } catch (Exception $e) {
            // 未知のエラー
            Log::error(sprintf("PF運営宛のメール通知に失敗しました。appId:%s kind:%s Exception:%s\n%s", 
                $id, $kind, $e->getMessage(), $e->getTraceAsString()));
        }
    }

    /**
     * デベロッパーの対象アプリかを判定する
     *
     * @param  mixed $appId
     * @param  mixed $device
     * @return bool
     */
    public function isDeveloperApplication($appId, $device = 'pc'){
        return $this->applyNotificationService->isDeveloperApplication($appId, $device);
    }
}
