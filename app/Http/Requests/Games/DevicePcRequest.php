<?php
namespace App\Http\Requests\Games;

use App\Http\Requests\Request;

/**
 * ゲーム登録：PC情報
 */
class DevicePcRequest extends Request
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $validate = [
            'id' => 'required',
            'description' => 'required|max:5000|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'description_middle' => 'required|max:20|platform_dependent|except_tag_html|restricted_characters',
            'how_to_play' => 'max:2000|platform_dependent|control_characters|except_tag_html|restricted_characters',
            'restrictions' => 'required|max:5000|platform_dependent|picture_characters|except_tag_html',
        ];

        if ($this->isEnableDomainType()) {
            if ($this->get('id') == 854854) {
                $validate['domain_type'] = 'required|integer|in:0,3';
            } else {
                $validate['domain_type'] = 'required|integer|in:1,3';
            }
        }

        return $validate;
    }

    public function attributes()
    {
        return [
            'id' => 'アプリID',
            'description' => '紹介文',
            'description_middle' => '紹介文（20文字）',
            'how_to_play' => 'ゲームの遊び方',
            'restrictions' => '対応機種',
            'domain_type' => 'ドメインタイプ',
        ];
    }

    public function customMessages()
    {
        return [
            'platform_dependent' => $this->MSG076,
            'control_characters' => $this->MSG261,
            'picture_characters' => $this->MSG077,
            'restricted_characters' => $this->MSG295,
            'domain_type.required' => $this->MSG241,
        ];
    }

    protected function redirectRules()
    {
        return [
            'device.pc.update' => [
                'action' => 'device.pc.edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ]
        ];
    }

    /**
     * ドメインタイプの指定が有効かを返す。
     * @return boolean ドメインタイプの指定が有効か
     */
    public function isEnableDomainType()
    {
        $apps = config('forms.Games.enableDomainTypeApps');

        if (empty($apps)) {
            // 設定自体がない場合は全開放
            return true;
        }

        foreach (explode(',', $apps) as $app) {
            $appIdDevice = explode(':', $app);

            if (in_array($this->get('id'), $appIdDevice) && in_array('pc', $appIdDevice)) {
                return true;
            }
        }

        return false;
    }
}
