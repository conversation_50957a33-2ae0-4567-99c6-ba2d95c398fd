<?php
namespace App\Http\Requests;

use App\Http\Requests\Request;

class NgwordFilterRequest extends Request
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'title'     => 'required|max:40',
        ];
    }

    public function attributes()
    {
        return [
            'title'     => 'タイトル',
        ];
    }

    protected function redirectRules()
    {
        return [
            'store' => [
                'action' => 'create',
            ],
            'update' => [
                'action' => 'edit',
                'parameters' => [
                    'id' => 'id'
                ]
            ],
        ];
    }
}
