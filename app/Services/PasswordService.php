<?php

namespace App\Services;

use App\Models\FreegameDeveloper\Developer;
use App\Models\FreegameDeveloper\DeveloperPasswordHistory;
use App\Models\FreegameDeveloper\FreegameDeveloper;
use Carbon\Carbon;

class PasswordService extends CustomService
{
    protected $developer;

    protected $developerPasswordHistory;

    public function __construct(Developer $developer, DeveloperPasswordHistory $developerPasswordHistory)
    {
        $this->developer = $developer;
        $this->developerPasswordHistory = $developerPasswordHistory;
    }

    /**
     * Get values for form select box,radio box
     *
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.Password');
        return [
            'screenName' => $privateConfigs['screenName'],
            'menuName'   => $privateConfigs['menuName'],
        ];
    }

    /**
     * update freegame_developer.developer password and add freegame_developer.devloper_password_history
     *
     * @param array $request ($request->all())
     * @return bool true: suuccess
     */
    public function updatePassword($request)
    {
        FreegameDeveloper::beginTransaction();
        try {
            // パスワード設定
            $editDeveloper['old_password']         = $request['password_now'];
            $editDeveloper['password']             = $request['password1'];
            $editDeveloper['password_update_date'] = Carbon::now();
            $this->developer->edit($editDeveloper, $request['id']);
    
            // 新規のパスワードをパスワードログに格納
            $this->developerPasswordHistory
                 ->addHistory($request['id'], $editDeveloper['password'], $editDeveloper['password_update_date']);

            FreegameDeveloper::commit();
        } catch (\Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * check password input now table
     *
     * @param int $id
     * @param string $password
     * @return boolean
     */
    public function isPasswordSame($id, $password)
    {
        return $this->developer->getOneForAuthById($id, $password);
    }
    /**
     * Check Old Password Same
     * @param int $id
     * @param string $password
     * @return boolean
     */
    public function isOldPasswordSame($id, $password)
    {
        // パスワード履歴テーブルに存在する場合
        return $this->developerPasswordHistory->getHistoryByIdAndPass($id, $password);
    }

    /**
     * Check User Exist
     * @param integer $id
     * @param string $loginId
     * @return boolean
     */
    public function checkUserExist($id, $loginId)
    {
        return (bool) $this->developer->checkUserExistByIdAndLoginId($id, $loginId);
    }

    /**
     * xhr Update Password
     * @param array $params
     * @return boolean
     */
    public function xhrUpdatePassword($params)
    {
        $params['password_update_date'] = Carbon::now();
        return $this->developer->xhrUpdatePassword($params);
    }
}
