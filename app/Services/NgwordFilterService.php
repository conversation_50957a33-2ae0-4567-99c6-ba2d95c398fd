<?php

namespace App\Services;

use App\Models\FreegameDeveloper\DeveloperNgwordApplications;
use Illuminate\Pagination\LengthAwarePaginator;

class NgwordFilterService extends CustomService
{
    protected $developerNgApp;
    protected $ngwordApi;

    const SESSION_KEY_PREFIX_EXCLUSIONS = 'session_key_ngword_exclusions_';

    public function __construct(DeveloperNgwordApplications $developerNgApp, NgwordApiService $ngwordApi)
    {
        $this->developerNgApp = $developerNgApp;
        $this->ngwordApi = $ngwordApi;
    }

    /**
     * Search ng word
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function getPaginator($request)
    {
        $params     = $this->getSearchParams($request);
        $pagerArray = $this->ngwordApi->getPagerArray($params);
        $apps       = $pagerArray['data'];
        $pagination = $pagerArray['pagination'];

        // 配列をページャー化
        $paginator = new LengthAwarePaginator(
            $apps,
            $pagination['total'],
            $pagination['per_page'],
            $pagination['current_page'],
            ['path' => LengthAwarePaginator::resolveCurrentPath()]
        );

        $paginator = $paginator->appends($params);
        return $paginator;
    }

    /**
     * id でアプリケーションを一件取得する 失敗した場合はfalseを返す
     * @param  integer $id
     * @return array|boolean
     */
    public function getAppById($id)
    {
        return $this->ngwordApi->getAppById($id);
    }

    /**
     * Insert
     * @param  array $request
     * @return boolean
     */
    public function insert($request)
    {
        return $this->ngwordApi->insert($request['title']);
    }

    /**
     * Update
     * @param  Request $request
     * @return boolean
     */
    public function edit($request)
    {
        return $this->ngwordApi->edit($request->get('id'), $request->get('title'));
    }

    /**
     * NGワード除外ワードの登録（更新）
     * @param Request $request
     */
    public function updateNgwordExclusions($request)
    {
        $sessionKey = self::SESSION_KEY_PREFIX_EXCLUSIONS . $request->get('id');
        if ($request->session()->has($sessionKey)) {
            $ngwordExclusions = $request->session()->get($sessionKey);
            $this->ngwordApi->storeNgwordExclusions($request->get('id'), $ngwordExclusions);
        }
    }

    /**
     * Delete
     * @param array $request
     * @return boolean
     */
    public function delete($request)
    {
        return $this->ngwordApi->delete($request['id']);
    }

    /**
     * Get values
     * @return array
     */
    public function getFormData()
    {
        $privateConfigs = config('forms.NgwordFilter');

        return [
            'level' => $privateConfigs['level'],
            'screenName'  => $privateConfigs['screenName'],
            'menuName'  => $privateConfigs['menuName'],
        ];
    }

    /**
     * Get search params from request
     * @param request $request
     * @return array
     */
    private function getSearchParams($request)
    {
        $params = [];
        $params['ids'] = null;

        if ($request->has('user_id')) {
            $params['ids'] = $this->getAppIds($request->get('user_id'));
        }

        // Pagination
        $params['perPage'] = $request->get('perPage', config('forms.common.pagination.perPage.0'));
        $params['page'] = $request->get('page', "1");

        return $params;
    }

    /**
     * ユーザーの閲覧可能なアプリケーションのIDを返します
     * @param int $userId
     * @return array
     */
    private function getAppIds($userId)
    {
        $condition['developer_id'] = $userId;
        $appIds = $this->developerNgApp->getList($condition)->toArray();

        if ($appIds) {
            $appIds = array_column($appIds, 'ngword_application_id');
        }
        return $appIds;
    }

    /**
     * Get search condition
     * @param array $search
     * @return array
     */
    public function formatSearchConditionExclusionsEdit($search = [])
    {
        if (request()->has('search')) {
            $search = session('NgwordFilter.ExclusionsEdit.search', []);
            request()->merge($search);
        }
        $search = array_only($search, [
            'filter_keyword',
            'perPage',
            'page'
        ]);
        request()->session()->set('NgwordFilter.ExclusionsEdit.search', $search);
        return $search;
    }

    /**
     * NGワードのページネーターを取得する。（除外されたNGワードは除く
     * @param Request $request
     * @param array $ngwordExclusions
     * @return LengthAwarePaginator
     */
    public function searchNgword($request, $ngwordExclusions)
    {
        $params = $this->getNgwordSearchParams($request);
        $params['exclusion_ids'] = array_column($ngwordExclusions, 'ngword_id');
        $pagerArray = $this->ngwordApi->searchNgword($params);

        $ngwords = $pagerArray['data'];
        $pagination = $pagerArray['pagination'];

        // 配列をページャー化
        $searchPaginator = new LengthAwarePaginator(
            $ngwords,
            $pagination['total'],
            $pagination['per_page'],
            $pagination['current_page'],
            ['path' => LengthAwarePaginator::resolveCurrentPath()]
        );

        unset($params['exclusion_ids']);
        $searchPaginator = $searchPaginator->appends($params);
        return $searchPaginator;
    }

    /**
     * セッションまたは API から除外されたNGワードリストを取得、
     * 除外ワードの減操作があれば、操作を実行した結果の除外ワードリストを返す
     * @param Request $request
     * @param int $applicationId
     * @return array
     */
    public function getExcludedNgwords($request, $applicationId)
    {
        $sessionKey = self::SESSION_KEY_PREFIX_EXCLUSIONS . $applicationId;

        if ($request->get('from_index')) {
            $ngwordExclusions = $this->ngwordApi->getNgwordExclusions($applicationId);
        } else {
            $ngwordExclusions = $request->session()->get($sessionKey, function () use ($applicationId) {
                return $this->ngwordApi->getNgwordExclusions($applicationId);
            });
        }

        // 削除ボタン押下時
        if ($request->get('remove-id')) {
            foreach ($ngwordExclusions as $index => $exclusion) {
                if ($exclusion['ngword_id'] == $request->get('remove-id')) {
                    unset($ngwordExclusions[$index]);
                    break;
                }
            }
        }

        if (is_array($ngwordExclusions) && $ngwordExclusions) {
            // ソート
            foreach ($ngwordExclusions as $index => $value) {
                $sort[$index] = $value['ngword_id'];
            }
            array_multisort($sort, SORT_ASC, $ngwordExclusions);
        }

        $request->session()->put($sessionKey, $ngwordExclusions);
        return $ngwordExclusions;
    }

    /**
     * 送られてきた除外NGワードをセッションに追加する
     * @param Request $request
     */
    public function addExcludedNgword($request)
    {
        $applicationId = $request->get('application_id');
        $sessionKey = self::SESSION_KEY_PREFIX_EXCLUSIONS . $applicationId;

        $ngwordExclusions = $request->session()->get($sessionKey, function () use ($applicationId) {
            return $this->ngwordApi->getNgwordExclusions($applicationId);
        });

        $ngwordExclusions[] = [
                'ngword_id' => $request->get('ngword_id'),
                'keyword'   => $request->get('keyword'),
                'ruby'      => $request->get('ruby'),
                'level'     => $request->get('level'),
        ];

        $request->session()->put($sessionKey, $ngwordExclusions);
    }

    /**
     * Get search params from request
     * @param Request $request
     * @return array
     */
    protected function getNgwordSearchParams($request)
    {
        $params = [];

        // Condition query
        $params['filter_keyword'] = $request->get('filter_keyword', '');

        // Pagination
        $params['perPage'] = $request->get('perPage', config('forms.common.pagination.perPage.0'));
        $params['page'] = $request->get('page', "1");

        return $params;
    }
    /**
     * Format search condition
     * @param array $params
     * @return array
     */
    public function formatSearchCondition($params)
    {
        if (request()->has('search')) {
           $params = session('NgwordFilter.search', []);
            request()->merge($params);
        }
        $search = array_only($params, [
            'perPage',
            'page',
        ]);
        request()->session()->set('NgwordFilter.search', $search);
        return $search;
    }
}
