<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\Freegame\User;
use App\Models\Freegame\UserImage;
use App\Models\FreegameCommunity\Community;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\FreegameDeveloper\ImageUserInspectionLog;
use Image;

/**
 * プロフィール画像管理
 */
class ProfileService extends CustomService
{
    protected $Application;
    protected $User;
    protected $UserImage;
    protected $Community;
    protected $DeveloperApplication;
    protected $ImageUserInspectionLog;

    public function __construct(
        Application            $Application,
        User                   $User,
        UserImage              $UserImage,
        Community              $Community,
        DeveloperApplication   $DeveloperApplication,
        ImageUserInspectionLog $ImageUserInspectionLog
    ) {
        $this->Application            = $Application;
        $this->User                   = $User;
        $this->UserImage              = $UserImage;
        $this->Community              = $Community;
        $this->DeveloperApplication   = $DeveloperApplication;
        $this->ImageUserInspectionLog = $ImageUserInspectionLog;
    }

    /**
     * プロフィール画像設定
     *
     * @param  array   $params
     *
     * @return boolean true
     *
     */
    public function editProfile($params)
    {
        if ($params['number'] != '0') {
            // オリジナル画像をプロフィール画像に設定
            $where = array('user_id' => $params['user_id']);
            $data  = array('priority' => 0);
            $this->UserImage->editByConditions($where, $data);

            $where = array('id' => $params['image_id'], 'user_id' => $params['user_id']);
            $data  = array('priority' => 1);
            $this->UserImage->editByConditions($where, $data);
        } else {
            // テンプレート画像をプロフィール画像に設定
            $data = array('thumbnail' => $params['image_id']);
            $this->User->edit($params['user_id'], $data);

            $where = array('user_id' => $params['user_id']);
            $data  = array('priority' => 0);
            $this->UserImage->editByConditions($where, $data);
        }

        return true;
    }

    /**
     * オリジナル画像アップロード
     *
     * @param  object  $params
     *
     * @return boolean true
     */
    public function uploadUserImage($params)
    {
        // ====================
        // 現在日時
        // ====================
        $nowTime = date('Y-m-d H:i:s');

        // ====================
        // サイト区分
        // ====================
        $site = '';
        $dataApplication = array();
        $object = $this->Application->getListByIds([$params['app_id']]);
        if ($object->count() > 0) {
            foreach ($object as $key => $val) {
                $dataApplication = $val->toArray();
                break;
            }
        }
        if ($dataApplication['general'] == '1') {
            $site = 'general';
        } else {
            $site = 'adult';
        }

        // ====================
        //監査テーブル登録
        // ====================
        $imageUserInspectionLogId = '';
        $data = array(
            'user_id'     => $params['user_id'],
            'type'        => 'profile',
            'regist_date' => $nowTime,
        );
        $imageUserInspectionLogId = $this->ImageUserInspectionLog->add($data);

        // ====================
        // オリジナル画像登録
        // ====================
        $userImageId = '';
        $where  = array('user_id' => $params['user_id'], 'number' => $params['number']);
        $object = $this->UserImage->getList($where);
        if ($object->count() > 0) {
            foreach ($object as $key => $val) {
                $userImageId = $val->id;
                break;
            }
        }
        if (empty($userImageId)) {
            $data = array(
                'user_id'     => $params['user_id'],
                'number'      => $params['number'],
                $site         => 1,
                'regist_date' => $nowTime,
            );
            $userImageId = $this->UserImage->add($data);
        } else {
            $data = array(
                'status'      => 'inspection',
                'regist_date' => $nowTime,
            );
            $where = array('user_id' => $params['user_id'], 'number' => $params['number']);
            $this->UserImage->editByConditions($where, $data);
        }

        // ====================
        // 画像アップロード
        // ====================
        $userId   = md5($params['user_id']);
        $userId1  = substr($userId, 0, 1);
        $userId2  = substr($userId, 1, 1);
        $fileDirc = '/user/'.$userId1.'/'.$userId2.'/'.$userId.'/'.$params['number'].'/';
        $orgPath = $params['file']->getRealPath();

        $convertList = [
            ['path' => 'small_mb.gif',     'width' => 25,  'height' => 25,  'format' => 'gif'],
            ['path' => 'large_mb.gif',     'width' => 48,  'height' => 76,  'format' => 'gif'],
            ['path' => 'huge_mb.gif',      'width' => 120, 'height' => 190, 'format' => 'gif'],
            ['path' => 'small_vga_mb.gif', 'width' => 50,  'height' => 50,  'format' => 'gif'],
            ['path' => 'large_vga_mb.gif', 'width' => 96,  'height' => 152, 'format' => 'gif'],
            ['path' => 'huge_vga_mb.gif',  'width' => 240, 'height' => 380, 'format' => 'gif'],
            ['path' => 'vga_mb.gif',       'width' => 96,  'height' => 96,  'format' => 'gif'],
            ['path' => 'mb.gif',           'width' => 48,  'height' => 48,  'format' => 'gif'],
            ['path' => 'small_sp.jpg',     'width' => 25,  'height' => 25,  'format' => 'jpg'],
            ['path' => 'large_sp.jpg',     'width' => 48,  'height' => 76,  'format' => 'jpg'],
            ['path' => 'huge_sp.jpg',      'width' => 120, 'height' => 190, 'format' => 'jpg'],
            ['path' => 'small_vga_sp.jpg', 'width' => 50,  'height' => 50,  'format' => 'jpg'],
            ['path' => 'large_vga_sp.jpg', 'width' => 96,  'height' => 152, 'format' => 'jpg'],
            ['path' => 'huge_vga_sp.jpg',  'width' => 240, 'height' => 380, 'format' => 'jpg'],
            ['path' => 'vga_sp.jpg',       'width' => 96,  'height' => 96,  'format' => 'jpg'],
            ['path' => 'sp.jpg',           'width' => 150,  'height' => 150,  'format' => 'jpg'],
        ];

        foreach ($convertList as $convert) {
            $tmpFile = tmpfile();
            $tmpPath = stream_get_meta_data($tmpFile)['uri'];
            $canvas = Image::canvas($convert['width'], $convert['height'], '#fff');
            $image = Image::make($orgPath)->resize($convert['width'], null, function ($constraint) {
                $constraint->aspectRatio();
            });
            $content = $canvas->insert($image, 'center')->encode($convert['format'], 100);
            fwrite($tmpFile, $content);
            $this->uploadFile($tmpPath, $convert['path'], $fileDirc);
            fclose($tmpFile);
        }

        // ====================
        // 監査画像アップロード
        // ====================
        $tmpFile = tmpfile();
        $tmpPath = stream_get_meta_data($tmpFile)['uri'];
        $canvas = Image::canvas(280, 280, '#fff');
        $image = Image::make($orgPath)->resize(280, null, function ($constraint) {
            $constraint->aspectRatio();
        });
        $content = $canvas->insert($image, 'center')->encode('gif', 100);
        fwrite($tmpFile, $content);
        $this->uploadFile($tmpPath, $imageUserInspectionLogId . '.gif', '/user/inspection/');
        fclose($tmpFile);

        // ====================
        // 監査テーブル更新
        // ====================
        $data = array('article_id' => $userImageId);
        $this->ImageUserInspectionLog->edit($imageUserInspectionLogId, $data);

        return true;
    }

    /**
     * オリジナル画像削除
     *
     * @param  integer $id
     *
     * @return boolean true
     */
    public function delUserImage($id)
    {
        $this->UserImage->del($id);

        return true;
    }

    /**
     * 権限のあるアプリケーションID配列取得
     *
     * @param  integer $developerId
     *
     * @return array   $devAppIds
     *
     */
    public function getDeveloperApplicationList($developerId)
    {
        $devAppIds = array();

        $object = $this->DeveloperApplication->getListByDeveloperId($developerId);
        if ($object->count() > 0) {
            foreach ($object as $key => $val) {
                $devAppIds[] = $val->app_id;
            }
        }

        return $devAppIds;
    }

    /**
     * 公式コミュニティ作成済みのアプリケーションID配列取得
     *
     * @param  array $devAppIds
     *
     * @return array $appIds
     *
     */
    public function getCommunitiesList($devAppIds)
    {
        $appIds = array();

        $params = ['type' => config('forms.Communities.type'), 'app_id' => $devAppIds,];
        $object = $this->Community->getList($params);
        if ($object->count() > 0) {
            foreach ($object as $key => $val) {
                $appIds[] = $val->app_id;
            }
        }

        return $appIds;
    }

    /**
     * 公式コミュニティ作成済みのアプリケーション配列取得
     *
     * @param  array $appIds
     *
     * @return array $listApplication
     *
     */
    public function getApplicationList($appIds)
    {
        $listApplication = array();

        $object = $this->Application->getListByIds($appIds, 'title');
        if ($object->count() > 0) {
            foreach ($object as $key => $val) {
                $listApplication[$val->id] = $val->title;
            }
        }

        return $listApplication;
    }

    /**
     * コミュニティ管理ユーザー取得
     *
     * @param  integer $appId
     *
     * @return array   $dataUser
     *
     */
    public function getCommunitiesManagerUser($appId)
    {
        $userId = '';
        $params = ['type' => config('forms.Communities.type'), 'app_id' => $appId];
        $object = $this->Community->getList($params);
        if ($object->count() > 0) {
            foreach ($object as $key => $val) {
                $userId = $val->manager_user_id;
                break;
            }
        }

        $dataUser = array();
        if (!empty($userId)) {
            $object = $this->User->getOne($userId);
            if ($object->count() > 0) {
                $dataUser = $object->toArray();
            }

            $dataUser['priority'] = 0;
            $dataUser['number']   = 0;
            $where  = array('user_id' => $userId);
            $object = $this->UserImage->getList($where);
            if ($object->count() > 0) {
                foreach ($object as $key => $val) {
                    if ($val->priority == 1) {
                        $dataUser['priority'] = $val->priority;
                        $dataUser['number']   = $val->number;
                        break;
                    }
                }
            }
        }

        return $dataUser;
    }

    /**
     * テンプレート画像配列取得
     *
     * @param  string $gender
     *
     * @return array
     *
     */
    public function getTemplateList($gender)
    {
        $listTemplate = array();

        $listImage = config('forms.Profile.listImage')[$gender];
        foreach ($listImage as $key => $val) {
            $list['id']   = $val;
            $list['path'] = env('HTTP_PICS_GENERAL_URL').'/freegame/profile/'.substr($val, 0, 1).'/'.$val.'/'.$val.'_huge_mb.gif';
            $listTemplate[] = $list;
        }

        return $listTemplate;
    }

    /**
     * オリジナル画像配列取得
     *
     * @param  integer $userId
     *
     * @return array   $listUserImage
     *
     */
    public function getUserImageList($userId)
    {
        $listUserImage = array();
        for ($number = 1; $number <= 3; $number++) {
            $list['id']     = '';
            $list['path']   = '';
            $list['number'] = $number;
            $listUserImage[] = $list;
        }

        $object = $this->UserImage->getList(['user_id' => $userId]);
        if ($object->count() > 0) {
            foreach ($object as $key => $val) {
                $userId  = md5($val->user_id);
                $userId1 = substr($userId, 0, 1);
                $userId2 = substr($userId, 1, 1);

                $list['id']      = $val->id;
                $list['path']    = env('HTTP_IMG_FREEGAMES_URL').'/user/'.$userId1.'/'.$userId2.'/'.$userId.'/'.$val->number.'/huge_mb.gif';
                $list['number']  = $val['number'];
                foreach ($listUserImage as $keyImage => $valImage) {
                    if ($valImage['number'] == $val['number']) {
                        $listUserImage[$keyImage] = $list;
                        break;
                    }
                }
            }
        }

        return $listUserImage;
    }

    /**
     * 権限のあるアプリケーションかチェック
     *
     * @param  integer $appId
     *
     * @return boolean
     *
     */
    public function checkStillDeveloper($appId)
    {
        if (!auth_is_user_sap()) {
            return false;
        }

        $devAppIds = $this->getDeveloperApplicationList(auth()->user()->id);

        if (count($devAppIds) > 0) {
            if (in_array($appId, $devAppIds)) {
                return true;
            }
        }
        return false;
    }

    /**
     * コミュニティ取得
     *
     * @param  array $params
     *
     * @return array $dataUser
     *
     */
    public function getCommunities($params = [])
    {
        return $this->Community->getList($params);
    }

    /**
     * ユーザープロフィール画像取得
     *
     * @param  integer $id
     *
     * @return object
     *
     */
    public function getUserImageOne($id)
    {
        return $this->UserImage->getOne($id);
    }
}
