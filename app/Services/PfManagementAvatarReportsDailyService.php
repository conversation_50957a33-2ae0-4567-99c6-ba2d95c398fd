<?php
namespace App\Services;

use App\Models\FreegameReport\AvatarPointDailyReport;

class PfManagementAvatarReportsDailyService extends CustomService
{

    protected $apDailyReport;

    public function __construct(AvatarPointDailyReport $apDailyReport)
    {
        $this->apDailyReport = $apDailyReport;
    }

//*********************************************************************************************************************
    /**
     * CSV用データ一覧取得
     * @param  array $param
     * @return array
     */
    public function getCsvList($param)
    {
        if (!auth_is_pf() && !auth_is_user_admin() && !auth_is_user_adminforpoint() && !auth_is_user_kc()) {
            return [];
        }

        $param = array_only($param, [
            'report_date_begin',
            'report_date_end',
        ]);

        $reportList = $this->apDailyReport->whereBetween('date', [$param['report_date_begin'], $param['report_date_end']])->orderBy('date')->orderBy('owner_id')->orderBy('game_id')->orderBy('app_id')->get();

        foreach ($reportList as $reportData) {
            $this->getMakeCsv($reportData);
        }

        return $reportList;
    }

    /**
     * CSV用データ整形
     * @param  array $reportData
     * @return array
     */
    private function getMakeCsv($reportData)
    {
        $config = config('forms.PfManagementAvatarReportsDaily');

        $reportData->date = date('Y/m/d', strtotime($reportData->date));

        $reportData->device = isset($config['namesDevice'][$reportData->device]) ? $config['namesDevice'][$reportData->device] : '';

        $reportData->site = ($reportData->site == $config['general']) ? $config['namesSite']['general'] : $config['namesSite']['adult'];
    }

//*********************************************************************************************************************
    /**
     * CSV用ヘッダデータ取得
     * @param  array $param
     * @return array
     */
    public function getCsvHeader()
    {
        return [
            'date' => '日付',
            'owner_id' => 'オーナーID',
            'owner_name' => 'オーナー名',
            'game_id' => 'ゲームID',
            'app_title' => 'タイトル',
            'device' => 'デバイス',
            'site' => 'サイト',
            'total_point' => '消費ポイント',
            'pay_point' => '有料ポイント',
            'domestic_total_point' => '国内ポイント',
            'foreign_total_point' => '海外ポイント',
        ];
    }

    /**
     * CSV用ファイル名取得
     * @param  array $param
     * @return string
     */
    public function getCsvFileName($param)
    {
        $config = config('forms.PfManagementAvatarReportsDaily');
        $reportDateBegin = isset($param['report_date_begin']) ? $param['report_date_begin'] : null;
        $reportDateEnd = isset($param['report_date_end']) ? $param['report_date_end'] : null;

        if (empty($reportDateBegin)) {
            $reportDateBegin = "{$config['minYear']}/01/01";
        }
        if (empty($reportDateEnd)) {
            $reportDateEnd = date('Y/m/d');
        }
        $fileName = sprintf(
            $config['csvFileName'], date('Y-m-d', strtotime($reportDateBegin)), date('Y-m-d', strtotime($reportDateEnd))
        );
        return $fileName;
    }

//*********************************************************************************************************************
    /**
     * セレクトボックス用配列取得：期間
     * @return array
     */
    public function getSelectPeriod()
    {
        $config = config('forms.PfManagementAvatarReportsDaily');

        return [
            $config['yesterday'] => $config['namesPeriod'][$config['yesterday']],
            $config['lastWeek'] => $config['namesPeriod'][$config['lastWeek']],
            $config['thisMonth'] => $config['namesPeriod'][$config['thisMonth']],
            $config['lastMonth'] => $config['namesPeriod'][$config['lastMonth']],
        ];
    }

//*********************************************************************************************************************
    /**
     * ページ設定取得
     * @return array
     */
    public function getFormData()
    {
        $config = config('forms.PfManagementAvatarReportsDaily');

        return [
            'formData' => [
                'screenName' => $config['screenName'],
                'breadcrumbs' => $config['breadcrumbsParent'],
            ],
            'minYear' => $config['minYear'],
            'maxYear' => $config['maxYear'],
            'namesPeriod' => $config['namesPeriod'],
            'selectPeriod' => $this->getSelectPeriod(),
        ];
    }
    
}
