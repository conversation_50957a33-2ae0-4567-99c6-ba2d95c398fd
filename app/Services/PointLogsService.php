<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\DeveloperApplication;
use App\Models\Freegame\PointLog;
use App\Models\Freegame\PointOrderEntry;
use Carbon\Carbon;
/**
 * 課金ログ比較ツール
 */
class PointLogsService extends CustomService
{
    protected $Application;
    protected $DeveloperApplication;
    protected $PointLog;
    protected $PointOrderEntry;
    protected $CouponApiService;

    public function __construct(
        Application          $Application,
        DeveloperApplication $DeveloperApplication,
        PointLog             $PointLog,
        PointOrderEntry      $PointOrderEntry,
        CouponApiService     $CouponApiService
    ) {
        $this->Application          = $Application;
        $this->DeveloperApplication = $DeveloperApplication;
        $this->PointLog             = $PointLog;
        $this->PointOrderEntry      = $PointOrderEntry;
        $this->CouponApiService     = $CouponApiService;
    }

    /**
     * CSVファイル名取得
     *
     * @param  object $condition
     *
     * @return string
     *
     */
    public function getCsvFileName($condition)
    {
        if (empty($condition['begin'])) {
            $condition['begin'] = '1970/01/01';
        }
        if (empty($condition['end'])) {
            $condition['end'] = date('Y/m/d');
        }
        $device = $condition['device'];
        if (is_array($device)) {
            $device = join('_', $device);
        }

        return sprintf(
            config('forms.PointLogs.CsvFileName'),
            $condition['app_id'],
            $device,
            date('Y-m-d', strtotime($condition['begin'])),
            date('Y-m-d', strtotime($condition['end']))
        );
    }

    /**
     * CSVダウンロード
     *
     * @param  object $condition
     *
     * @return object
     *
     */
    public function download($condition)
    {
        // getdevice
        $getDevice = function ($passportId, $model) {
            switch ($passportId) {
                case 'iphone':
                    return 'SmartPhone';
                case 'android':
                    if ($model == 'emulator') {
                        return 'Emulator';
                    } else {
                        return 'Smartphone';
                    }
                default:
                    return 'PC';
            }
        };

        // csvdownload
        $out = function ($vals) {
            $line = '';
            foreach ($vals as $val) {
                $line .= '"' . preg_replace('/\"/u', '""', $val) . '",';
            }
            $line = rtrim($line, ',') . "\r\n";
            echo mb_convert_encoding($line, 'SJIS-win', 'UTF-8');
        };

        // ダウンロードするcsvのヘッダー
        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename=' . $this->getCsvFileName($condition));

        /**
         * ヘッダー(一行目)
         */
        $header = [
            'DMM側',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            'SAP側',
            '',
            '',
            '',
            ''
        ];
        call_user_func($out, $header);

        /**
         * ヘッダー(二行目)
         */
        $header = [
            'device',
            'payment_id',
            'unit_price(単価)',
            'quantity(個数)',
            'total_point(合計ポイント)',
            'pay_amount_coupon(クーポン値引ポイント)',
            'discount_use_point(クーポン値引後消費ポイント)',
            'publisher_floor_id',
            '比較ステータス',
            'payment_id',
            'unit_price(単価)',
            'quantity(個数)',
            'total_point(合計ポイント)'
        ];
        call_user_func($out, $header);

        /**
         * SAP側課金ログ
         */
        $listSap = [];
        $fpSap = false;
        if ($condition->hasFile('sap_point')) {
            $fpSap = fopen($condition->file('sap_point')->getRealPath(), 'r');
            while (true) {
                $pos = ftell($fpSap);
                $data = fgetcsv($fpSap);
                if ($data === false) {
                    break;
                }
                if (empty($data[0]) || !preg_match('/^[0-9-]+$/', $data[0])) {
                    continue;
                }
                $listSap[$data[0]][] = $pos;
            }
        }

        /**
         * point_order_entry.id の範囲取得
         */
        $ids = $this->getPointOrderEntryId($condition);
        $minPointOrderEntryId = $ids[0];
        $maxPointOrderEntryId = $ids[1];

        /**
         * point_log.id の範囲取得
         */
        $ids = $this->getPointLogId($condition);
        $minPointLogId = $ids[0];
        $maxPointLogId = $ids[1];

        /**
         * DBとSAP側課金ログのデータ比較
         */
        $params = [
            'min_point_pog_id'         => $minPointLogId,
            'max_point_pog_id'         => $maxPointLogId,
            'app_id'                   => $condition['app_id'],
            'begin'                    => date('Y-m-d 00:00:00', strtotime($condition['begin'])),
            'end'                      => date('Y-m-d 23:59:59', strtotime($condition['end'])),
            'assoc'                    => true,
        ];
        if (!empty($condition['device'])) {
            if (count($condition['device']) == count(config('forms.PointLogs.deviceType'))) {
                // デバイスが全て選択されている場合は条件に入れない
            } else {
                $params['device'] = $condition['device'];
            }
        }

        /**
         * poind_order_entryのidを基準にして、2万ごとに対象課金データ取得
         */
        $unitCount = 20000;

        $poeFrom = $minPointOrderEntryId;
        $poeLast = $maxPointOrderEntryId;
        $plPaymentIds = [];

        while ($poeFrom <= $poeLast) {
            $poeTo = $poeFrom + $unitCount;

            if ($poeTo > $poeLast) {
                $poeTo = $poeLast;
            }

            $params['min_point_order_entry_id'] = $poeFrom;
            $params['max_point_order_entry_id'] = $poeTo;

            $result = array();
            $publisherFloorIdList = array();
            $couponResults = array();
            if(!empty($minPointOrderEntryId) &&
                !empty($maxPointOrderEntryId) &&
                !empty($minPointLogId) &&
                !empty($maxPointLogId)) {
                $result = $this->PointOrderEntry->getListWithPointLog($params);
                $couponResults = $this->PointOrderEntry->getListWithItemPurchaseOrderLog($params);

                // クーポン関連のポイント情報がある時のみ処理する
                if (!empty($couponResults)) {
                    // クーポンIDに対応する費用負担元の配列を作成
                    $couponIds = collect($couponResults)->pluck('coupon_id')->filter(function ($item) {
                        return !empty($item);
                    })->unique()->values()->all();

                    // クーポン決済が行われている場合のみ API 実行してフロア情報を取得する
                    if (!empty($couponIds)) {
                        // クーポンAPIからクーポン詳細情報を取得する
                        $apiResponse = $this->CouponApiService->getCouponDetail($couponIds);

                        if ($apiResponse['resultCode'] != 200) {
                            throw new Exception($apiResponse['resultMessage']);
                        } else if (!empty($apiResponse['response'])) {
                            // ステータスが正常かつレスポンスにクーポン情報が正しく渡された場合のみフロア情報を設定
                            foreach ($apiResponse['response']['coupons'] as $couponDetail) {
                                $publisherFloorIdList[$couponDetail['id']] = $couponDetail['publisher_floor_id'];
                            }
                        }
                    }

                    // paymentIDに対応する使用クーポンの値引額とIDの配列を作成
                    $useCouponList = array();
                    foreach ($couponResults as $couponResult) {
                        $useCouponList[$couponResult['payment_id']] = [
                            'pay_amount_coupon' => $couponResult['discount_price'],
                            'coupon_id'         => $couponResult['coupon_id'],
                        ];
                    }

                    // 課金比較のためのデータがある時は、クーポンに関する課金情報を追加する
                    if (!empty($result)) {
                        // クーポンに関する情報をCSV出力リストに反映
                        foreach ($result as $key => $data) {
                            if (!array_key_exists($data['payment_id'], $useCouponList)) {
                                continue;
                            } else {

                                $useCoupon = $useCouponList[$data['payment_id']];
                                $result[$key]['coupon_id'] = $useCoupon['coupon_id'];
                                $result[$key]['pay_amount_coupon'] = $useCoupon['pay_amount_coupon'];

                            }

                            if (array_key_exists($useCoupon['coupon_id'], $publisherFloorIdList)) {
                                $result[$key]['publisher_floor_id'] = $publisherFloorIdList[$useCoupon['coupon_id']];
                            }
                        }
                    }

                    // item_purchase_order_log にしかデータがない場合を考慮
                    foreach ($couponResults as $key => $data) {
                        // すでにCSV出力データに課金データがある場合はskip
                        if (in_array($data['payment_id'], array_column($result, 'payment_id'))) {
                            continue;
                        }

                        $publisherFloorId = "";
                        if (array_key_exists($data['coupon_id'], $publisherFloorIdList)) {
                            $publisherFloorId = $publisherFloorIdList[$data['coupon_id']];
                        }

                        $result[] = [
                                        'payment_id' => $data['payment_id'],
                                        'passport_id' => $data['passport_id'],
                                        'model' => $data['model'],
                                        'unit_price' => $data['unit_price'],
                                        'quantity' => $data['quantity'],
                                        'coupon_id' => $data['coupon_id'],
                                        'pay_amount_coupon' => $data['discount_price'],
                                        'publisher_floor_id' => $publisherFloorId
                                    ];
                    }
                }
            }

            // payment_id の昇順で並び替える
            array_multisort(array_column($result, "payment_id"), SORT_ASC, $result);

            // csv出力データを算出する
            foreach ($result as $data) {

                // 最小値/最大値のＩＤ指定で取得したうち、期間外のデータが含まれる場合があるのでフィルタリングする。
                if (array_key_exists("date", $data)) {
                    if(! Carbon::parse($data['date'])->between(new Carbon($condition['begin']), new Carbon($condition['end']." 23:59:59"))) {
                        continue;
                    }
                }

                $tmpList                    = [];
                $tmpList['dmm_device']      = call_user_func($getDevice, $data['passport_id'], $data['model']);
                $tmpList['dmm_payment_id']  = $data['payment_id'];
                $tmpList['dmm_unit_price']  = $data['unit_price'];
                $tmpList['dmm_quantity']    = $data['quantity'];
                $tmpList['dmm_total_point'] = ($tmpList['dmm_unit_price'] * $tmpList['dmm_quantity']);
                if (array_key_exists("pay_amount_coupon", $data)) {
                    $tmpList['dmm_pay_amount_coupon'] = $data['pay_amount_coupon'];
                } else {
                    $tmpList['dmm_pay_amount_coupon'] = 0;
                }
                $tmpList['dmm_discount_use_point'] = $tmpList['dmm_total_point'] - $tmpList['dmm_pay_amount_coupon'];
                if (array_key_exists("coupon_id", $data) && array_key_exists($data['coupon_id'], $publisherFloorIdList)) {
                    $tmpList['dmm_publisher_floor_id'] = $publisherFloorIdList[$data['coupon_id']];
                } else {
                    $tmpList['dmm_publisher_floor_id'] = '';
                }
                $tmpList['sap_status']      = 'DMM側のみ';
                $tmpList['sap_payment_id']  = '';
                $tmpList['sap_unit_price']  = '';
                $tmpList['sap_quantity']    = '';
                $tmpList['sap_total_point'] = '';
                if (!isset($plPaymentIds[$data['payment_id']])) {
                    $plPaymentIds[$data['payment_id']] = true;
                    if (isset($listSap[$data['payment_id']])) {
                        if (isset($listSap[$data['payment_id']][1])) {
                            $tmpList['sap_status'] = '双方(重複)';
                        } else {
                            $tmpList['sap_status'] = '双方に存在';
                        }
                        fseek($fpSap, $listSap[$data['payment_id']][0]);
                        $csvList = fgetcsv($fpSap);
                        $tmpList['sap_payment_id']  = array_get($csvList, '0');
                        $tmpList['sap_unit_price']  = array_get($csvList, '1', 0);
                        $tmpList['sap_quantity']    = array_get($csvList, '2', 0);
                        $tmpList['sap_total_point'] = $tmpList['sap_unit_price'] * $tmpList['sap_quantity'];
                        unset($listSap[$data['payment_id']][0]);
                        if (empty($listSap[$data['payment_id']])) {
                            unset($listSap[$data['payment_id']]);
                        }
                        unset($csvList);
                    }
                }
                call_user_func($out, $tmpList);
                unset($tmpList);
            }

            $poeFrom = $poeTo + 1;
        }

        /**
         * SAP側課金ログの残り
         */
        foreach ($listSap as $posList) {
            if (isset($posList[1])) {
                $status = '双方(重複)';
            } else {
                $status = 'SAP側のみ';
            }
            foreach ($posList as $pos) {
                fseek($fpSap, $pos);
                $csvList = fgetcsv($fpSap);
                $paymentId = '';
                if (isset($plPaymentIds[$csvList[0]])) {
                    $paymentId = $csvList[0];
                }
                $tmpList                    = [];
                $tmpList['dmm_device']      = '';
                $tmpList['dmm_payment_id']  = $paymentId;
                $tmpList['dmm_unit_price']  = '';
                $tmpList['dmm_quantity']    = '';
                $tmpList['dmm_total_point'] = '';
                $tmpList['dmm_pay_amount_coupon']  = '';
                $tmpList['dmm_discount_use_point']    = '';
                $tmpList['dmm_publisher_floor_id'] = '';
                $tmpList['sap_status']      = $status;
                $tmpList['sap_payment_id']  = array_get($csvList, '0');
                $tmpList['sap_unit_price']  = array_get($csvList, '1', 0);
                $tmpList['sap_quantity']    = array_get($csvList, '2', 0);
                $tmpList['sap_total_point'] = $tmpList['sap_unit_price'] * $tmpList['sap_quantity'];
                unset($csvList);

                call_user_func($out, $tmpList);
                unset($tmpList);
            }
        }

        if ($fpSap) {
            fclose($fpSap);
        }
    }

    /**
     * point_order_entry.id の範囲取得
     *
     * @param  object $condition
     *
     * @return array
     *
     */
    public function getPointOrderEntryId($condition)
    {
        $minId = 0;
        $maxId = 0;

        $params = array();
        if (!empty($condition['app_id'])) {
            $params['app_id'] = $condition['app_id'];
        }
        if (!empty($condition['begin'])) {
            $params['begin'] = date('Y/m/d', strtotime($condition['begin'].' -1 day')).' 23:00:00';
        }
        if (!empty($condition['end'])) {
            $params['end'] = $condition['end'].' 23:59:59';
        }

        $object = $this->PointOrderEntry->getRangeId($params);
        if (isset($object->min_id)) {
            $minId = $object->min_id;
        }
        if (isset($object->max_id)) {
            $maxId = $object->max_id;
        }

        return [$minId, $maxId];
    }

    /**
     * point_log.id の範囲取得
     *
     * @param  object $condition
     *
     * @return array
     *
     */
    public function getPointLogId($condition)
    {
        $minId = 0;
        $maxId = 0;

        $params = array();
        if (!empty($condition['begin'])) {
            $params['begin'] = $condition['begin'].' 00:00:00';
        }
        if (!empty($condition['end'])) {
            $params['end'] = $condition['end'].' 23:59:59';
        }

        $object = $this->PointLog->getRangeId($params);
        if (isset($object->min_id)) {
            $minId = $object->min_id;
        }
        if (isset($object->max_id)) {
            $maxId = $object->max_id;
        }

        return [$minId, $maxId];
    }

    /**
     * 全アプリケーション一覧取得
     *
     * @param  array $params
     *
     * @return array $list
     *
     */
    public function getApplicationList()
    {
        $list = array();

        if (auth_is_user_sap()) {
            $object = $this->DeveloperApplication->getListByDeveloperId(['developer_id' => auth_user_id()]);
            if (count($object->count()) > 0) {
                $ids = array();
                foreach ($object as $data) {
                    $ids[] = $data->app_id;
                }
                if (count($ids) > 0) {
                    $object = $this->Application->getApplicationTitleList(['id' => $ids]);
                    if (count($object->count()) > 0) {
                        foreach ($object as $data) {
                            $list[$data->id] = $data->title;
                        }
                    }
                }
            }
        } else {
            $object = $this->Application->getApplicationTitleList();
            if (count($object->count()) > 0) {
                foreach ($object as $data) {
                    $list[$data->id] = $data->title;
                }
            }
        }

        return $list;
    }
}
