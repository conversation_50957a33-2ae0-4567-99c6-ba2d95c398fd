<?php
namespace App\Services;

use App\Libs\OAuth\CurlClient;
use App\Libs\OAuth\PrivateNetgameApiOAuth;

use OAuth\Common\Http\Uri\Uri;
use OAuth\Common\Consumer\Credentials;
use OAuth\OAuth1\Token\StdOAuth1Token;
use OAuth\OAuth1\Signature\Signature;
use OAuth\Common\Storage\Memory;

/**
 * Class PrivateNetgameApi
 * @package App\Services
 */
class PrivateNetgameApi
{
    /**
     * @var array
     */
    protected $defaultOptions = array(
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_CONNECTTIMEOUT => 20,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_AUTOREFERER => true,
        CURLOPT_FOLLOWLOCATION => true,
    );

    /**
     * PrivateNetgameApi constructor.
     */
    public function __construct()
    {
        $this->config = $this->getConfig();
    }

    /**
     * POST 送信を実施します
     * @param string $uri
     * @param array $params
     * @return array
     */
    public function post($uri, $params = array())
    {
        return $this->request("POST", $uri, $params);
    }

    /**
     * OAuth リクエストを送信します
     * @param string $method
     * @param string $uri
     * @param array $params
     * @return array
     */
    public function request($method, $uri, $params = array())
    {
        $method = strtoupper($method);

        $consumer = $this->getConsumer();
        // $signatureMethod = $this->getSignatureMethod();
        $accessToken = null;
        $endpoint = $this->getEndpointUrl($uri);

        $signature = new Signature($consumer);
        $accessToken = new StdOAuth1Token(null);

        $httpClient = new CurlClient();
        $httpClient->setCurlParameters($this->getCurlOptions());

        $storage = new Memory();

        $oauthUri = new Uri($endpoint);
        $oauth = new PrivateNetgameApiOAuth($consumer, $httpClient, $storage, $signature, $oauthUri);
        $oauth->setAccessToken($accessToken);

        $data = $oauth->request($oauthUri, $method, $params);

        $status = [
            "http_code" => $data["code"],
        ];
        $body = json_decode($data['body'], true);

        return compact('status', 'body');
    }

    /**
     * OAuth Consumer を返却します
     * @return \OAuth\Common\Consumer\Credentials
     */
    protected function getConsumer()
    {
        return new Credentials(
            $this->config["consumer_key"],
            $this->config["consumer_secret"],
            null
        );
    }

    /**
     * エンドポイントURLを返却します
     * @param $url
     * @return string
     */
    protected function getEndpointUrl($url)
    {
        return $this->config["endpoint"] . $url;
    }

    /**
     * curl の接続オプションを返却します
     * @return array
     */
    protected function getCurlOptions()
    {
        $options = $this->defaultOptions;
        foreach ([
            CURLOPT_TIMEOUT => "timeout",
            CURLOPT_SSL_VERIFYPEER => "verify",
            CURLOPT_SSL_VERIFYHOST => "verify",
        ] as $curlField => $f) {
            if (isset($this->config["curl"][$f])) {
                $options[$curlField] = $this->config["curl"][$f];
            }
        }
        return $options;
    }

    /**
     * 設定情報を返却します。
     * @return array
     */
    protected function getConfig()
    {
        return config('private-netgame-api');
    }
}
