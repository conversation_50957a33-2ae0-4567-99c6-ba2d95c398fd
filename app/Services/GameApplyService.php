<?php
namespace App\Services;

use App\Models\Freegame\Application;
use App\Models\FreegameDeveloper\FreegameDeveloper;
use App\Models\FreegameDeveloper\GameApply;
use App\Models\FreegameDeveloper\GameContact;
use App\Models\FreegameDeveloper\GameEocs;
use App\Models\FreegameInspection\FreegameInspection;

class GameApplyService extends CustomService
{
    protected $application;
    protected $gameApply;
    protected $gameContact;
    protected $gameEocs;
    protected $freegameInspection;
    protected $controllerName;

    public function __construct(
        Application        $application,
        GameApply          $gameApply,
        GameContact        $gameContact,
        GameEocs           $gameEocs,
        FreegameInspection $freegameInspection
    ) {
        $this->application        = $application;
        $this->gameApply          = $gameApply;
        $this->gameContact        = $gameContact;
        $this->gameEocs           = $gameEocs;
        $this->freegameInspection = $freegameInspection;
    }

//*********************************************************************************************************************
    /**
     * 入力データ取得：登録用
     * @param  \Illuminate\Http\Request $request
     * @return array
     */
    public function getCreateRequestData($request)
    {
        if ($request->all() && $request->isMethod('post')) {
            $data = $request->all();
        } elseif ($request->old()) {
            $data = $request->old();
        } else {
            $data = [];
        }
        return $this->delEmptyElem($data);
    }

    /**
     * 入力データ取得：更新用
     * @param  \Illuminate\Http\Request $request
     * @param  integer                  $applyId
     * @return array
     */
    public function getEditRequestData($request, $applyId = null)
    {
        $applyId  = $applyId ?: request('id', old('id'));

        if ($request->all() && $request->isMethod('post')) {
            $data = $request->all();
        } elseif ($request->old()) {
            $data = $request->old();
        } else {
            $data = $this->getDetail($applyId);
        }
        return $this->delEmptyElem($data);
    }

    /**
     * 複数行入力系：1行目以外の空白行削除
     * @param object $applyData
     */
    private function delEmptyElem($applyData)
    {
        $reqContact    = isset($applyData['contact']) ?  $applyData['contact'] : [];
        $reqEocs       = isset($applyData['eocs'])    ?  $applyData['eocs']    : [];

        // 1行目取得
        $resContent[]  = array_shift($reqContact) ?: [];
        $resEocs[]     = array_shift($reqEocs)    ?: [];

        // 連絡先情報整理
        foreach ($reqContact as $contact) {
            if (isset($contact['name']) && $contact['name']
            &&  isset($contact['tel'])  && $contact['tel']
            &&  isset($contact['mail']) && $contact['mail']
            &&  isset($contact['contact'])) {
                $resContent[]  = $contact;
            }
        }
        // ソフ倫受理番号詳細整理
        foreach ($reqEocs as $eocs) {
            if (isset($eocs['company']) && $eocs['company']
            &&  isset($eocs['title'])   && $eocs['title']
            &&  isset($eocs['number'])  && $eocs['number']) {
                $resEocs[]     = $eocs;
            }
        }
        $applyData['contact']  = $resContent;
        $applyData['eocs']     = $resEocs;
        return $applyData;
    }

//*********************************************************************************************************************
    /**
     * 入力遷移先取得
     * @param  \Illuminate\Http\Request $request
     * @param  string                   $current
     * @param  static                   $type
     * @return array
     */
    public function getTargetUrl($request, $current, $type)
    {
        $current  = strtolower($current);
        $data     = '';

        if ($request->all() && $request->isMethod('post')) {
            $data = $request->all();
        } elseif ($request->old()) {
            $data = $request->old();
        }

        if ($data) {
            $next = $this->getNextTargetUrl($data, $current, $type);
            $back = $this->getBackTargetUrl($data, $current, $type);

            return compact('next', 'back');
        }
        return [];
    }

    /**
     * 入力遷移先取得
     * @param  array  $data
     * @param  string $current
     * @param  static $type
     * @return array
     */
    private function getNextTargetUrl($data, $current, $type)
    {
        $config   = config('forms.GameApply');
        $next     = '';

        switch ($current) {
            case "{$type}":
                $next     = 'contact';
                break;

            case "{$type}contact":
                if ($data['asct_display']  == $config['exists']) {
                    $next = 'asct';
                    break;
                }
                // no break

            case "{$type}asct":
                if ($data['eocs_material'] == $config['exists']) {
                    $next = 'eocs';
                    break;
                }
                // no break

            case "{$type}eocs":
                $next     = 'confirm';
                break;

            case "{$type}confirm":
                $next     = $type == 'create' ? 'store' : 'update';
                break;
        }
        return $type.$next;
    }

    /**
     * 入力遷移先取得
     * @param  array  $data
     * @param  string $current
     * @param  static $type
     * @return array
     */
    private function getBackTargetUrl($data, $current, $type)
    {
        $config   = config('forms.GameApply');
        $back     = '';

        switch ($current) {
            case "{$type}store":
            case "{$type}update":
                $back     = 'confirm';
                break;

            case "{$type}confirm":
                if ($data['eocs_material'] == $config['exists']) {
                    $back = 'eocs';
                    break;
                }
                // no break

            case "{$type}eocs":
                if ($data['asct_display']  == $config['exists']) {
                    $back = 'asct';
                    break;
                }
                // no break

            case "{$type}asct":
                $back     = 'contact';
                break;

            case "{$type}contact":
                $back     = '';
                break;
        }
        return $type.$back;
    }

//*********************************************************************************************************************
    /**
     * 詳細取得
     * @param  integer $applyId
     * @return array
     */
    public function getDetail($applyId)
    {
        $applyData = $this->gameApply->getByIdAndDeveloper($applyId, auth_user_id());

        if ($applyData) {
            $this->setDeviceDetail($applyData);
            $this->setContactDetail($applyData);
            $this->setEocsDetail($applyData);
        }
        return $applyData;
    }

    /**
     * Device情報設定
     * @param array $applyData
     */
    private function setDeviceDetail($applyData)
    {
        $names   = config('forms.GameApply.namesDevice');
        $enable  = [];

        foreach ($names as $key => $name) {
            $tmp = "device_{$key}";

            if (!$applyData->{$tmp}) {
                continue;
            }
            $enable[] = $key;
        }
        $applyData->device  = $enable;
    }

    /**
     * Contact情報設定
     * @param array $applyData
     */
    private function setContactDetail($applyData)
    {
        $applyId            = $applyData->id;
        $contactDataList    = $this->gameContact->getListByApplyId($applyId);
        $contactDataList    = $contactDataList->toArray();

        $applyData->contact = $contactDataList;
    }

    /**
     * Eocs情報設定
     * @param array $applyData
     */
    private function setEocsDetail($applyData)
    {
        $applyId            = $applyData->id;
        $eocsDataList       = $this->gameEocs->getListByApplyId($applyId);
        $eocsDataList       = $eocsDataList->toArray();

        $applyData->eocs    = $eocsDataList;
    }

//*********************************************************************************************************************
    /**
     * 検索一覧取得
     * @param  array $param
     * @return array
     */
    public function getSearchList($param)
    {
        // 検索条件設定
        if (request()->has('search')) {
            $param  = session("{$this->controllerName}.search", []);
            request()->merge($param);
        }
        if (! isset($param['perPage'])) {
            $param += ['perPage'      => config('forms.GameApply.perPage')];
        }
        if (! isset($param['page'])) {
            $param  += ['page' => 1];
        }

        $param      = array_only($param, [
            'perPage',
            'page'
        ]);
        $appends    = $param;
        request()->session()->set("{$this->controllerName}.search", $param);

        // その他条件
        $param     += ['developer_id' => auth_user_id()];

        // 取得
        $applyDataList = $this->gameApply->getList($param, 'id desc');
        $applyDataList->appends($appends);

        return $applyDataList;
    }

//*********************************************************************************************************************
    /**
     * 再申請可能か確認
     * @param  array $imgData
     * @return boolean
     */
    public function isEnableEdit($applyData)
    {
        if ($applyData['status'] == config('forms.GameApply.rejection')
        ||  $applyData['status'] == config('forms.GameApply.approval')) {
            return true;
        }
        return false;
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  array $param
     * @return boolean
     */
    public function createStore($param)
    {
        if (empty($param)) {
            return false;
        }

        // AppID作成
        $tblNumber = $this->freegameInspection->getInspectionTableNumber();
        $createIds = $this->getCreateAppId($tblNumber);
        $appId = $this->getCheckAppId($createIds);

        FreegameDeveloper::beginTransaction();
        try {
            // 基本情報/特定商取引法に基づく表記/申請情報
            $applyId = $this->addGameApply($appId, $param);

            // 連絡先情報
            $this->addGameContact($applyId, $param);

            // ソフ倫受理番号詳細
            $this->addGameEocs($applyId, $param);

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }

        // メール送信
        $this->sendMail($applyId, 'sendapply');

        return true;
    }

    /**
     * 更新
     * @param  array $param
     * @return boolean
     */
    public function updateStore($param)
    {
        if (empty($param)) {
            return false;
        }

        // ApplyID
        $applyId = $param['id'];
        $applyData = $this->getDetail($applyId);
        if (empty($applyData->exists)) {
            return false;
        }

        FreegameDeveloper::beginTransaction();
        try {
            // 基本情報/特定商取引法に基づく表記/申請情報
            $this->editGameApply($applyId, $param);

            // 連絡先情報
            $this->editGameContact($applyId, $param);

            // ソフ倫受理番号詳細
            $this->editGameEocs($applyId, $param);

            FreegameDeveloper::commit();
        } catch (Exception $e) {
            FreegameDeveloper::rollback();
            throw $e;
        }

        // メール送信
        $this->sendMail($applyId, 'sendapply');

        return true;
    }

    /**
     * 登録：基本情報/特定商取引法に基づく表記/申請情報
     * @param integer $appId
     * @param array   $applyData
     * @return integer $applyId
     */
    private function addGameApply($appId, $applyData)
    {
        // 基本情報/特定商取引法に基づく表記/申請情報/ステータス
        $attr = $this->getGameApplyAttr($applyData);

        // 申請
        $attr['developer_id'] = auth_user_id();
        $attr['app_id'] = $appId;
        $result = $this->gameApply->add($attr);
        if (!empty($result->id)) {
            return $result->id;
        }
        return 0;
    }

    /**
     * 更新：基本情報/特定商取引法に基づく表記/申請情報
     * @param  integer $applyId
     * @param  array   $applyData
     * @return integer $applyId
     */
    private function editGameApply($applyId, $applyData)
    {
        // 基本情報/特定商取引法に基づく表記/申請情報/ステータス
        $attr = $this->getGameApplyAttr($applyData);

        // 再申請
        $attr['rejection_reason'] = '';
        $this->gameApply->edit($applyId, $attr);
        return $applyId;
    }

    /**
     * 登録データ取得：基本情報/特定商取引法に基づく表記/申請情報/ステータス
     * @param  array $applyData
     * @return array $attr
     */
    private function getGameApplyAttr($applyData)
    {
        // 基本情報/特定商取引法に基づく表記/申請情報
        $attr = array_only($applyData, [
            'company',
            'game',
            'game_ruby',
            'type',
            'eocs_material',
            'asct_display',
            'redmine_url',
            'dealer_name',
            'dealer_addr',
            'dealer_tel',
            'office_hours',
            'sales_manager',
            'notification_mail',
            'comment'
        ]);
        // 基本情報 > 対応デバイス
        $notExistsVal = config('forms.GameApply.notExists');
        $existsVal = config('forms.GameApply.exists');
        $deviceList = array_keys(config('forms.GameApply.namesDevice'));
        $checkedDeviceList = array_get($applyData, 'device', []);
        foreach ($deviceList as $device) {
            $attr["device_{$device}"] = in_array($device, $checkedDeviceList, true) ? $existsVal : $notExistsVal;
        }
        // ステータス：申請中
        $attr['status'] = config('forms.GameApply.apply');
        return $attr;
    }

    /**
     * 登録：連絡先情報
     * @param integer $applyId
     * @param array   $applyData
     */
    private function addGameContact($applyId, $applyData)
    {
        $list = array_get($applyData, 'contact', []);
        foreach ($list as $data) {
            $attr = array_only($data, [
                'name',
                'tel',
                'mail',
            ]);
            $attr['apply_id'] = $applyId;
            $this->gameContact->add($attr);
        }
    }

    /**
     * 更新：連絡先情報
     * @param integer $applyId
     * @param array   $applyData
     */
    private function editGameContact($applyId, $applyData)
    {
        $this->gameContact->delByApplyId($applyId);
        $this->addGameContact($applyId, $applyData);
    }

    /**
     * 登録：ソフ倫受理番号詳細
     * @param integer $applyId
     * @param array   $applyData
     */
    private function addGameEocs($applyId, $applyData)
    {
        $list = array_get($applyData, 'eocs', []);
        foreach ($list as $data) {
            $attr = array_only($data, [
                'company',
                'title',
                'number'
            ]);
            $attr['apply_id'] = $applyId;
            $this->gameEocs->add($attr);
        }
    }

    /**
     * 更新：ソフ倫受理番号詳細
     * @param integer $applyId
     * @param array   $applyData
     */
    private function editGameEocs($applyId, $applyData)
    {
        $this->gameEocs->delByApplyId($applyId);
        $this->addGameEocs($applyId, $applyData);
    }

//*********************************************************************************************************************
    /**
     * メール送信
     * @param integer $applyId
     * @param string  $type
     */
    private function sendMail($applyId, $type)
    {
        if (!env('MAIL_SEND_AVAILABLE',true)) return;

        try {
            $this->sendXmlRpcData([
                    'message'  => 'Developer_Mail.SendMail',
                    'params'   => [
                            'apply_id' => $applyId,
                            'type'     => $type,
                    ]
            ]);
        } catch (Exception $e) {
            return [
                    'message'  => 'メールの送信に失敗しました。'
            ];
        }
    }

    /**
     * アプリID（app_id）の候補をランダムで10個生成する
     * -------------------------------------------
     * 各$numberの値におけるランダム生成の制限範囲
     * 1  =>      1 - 99999
     * 2  => 100000 - 199999
     * 3  => 200000 - 299999
     * 4  => 300000 - 399999
     * 5  => 400000 - 499999
     * 6  => 500000 - 599999
     * 7  => 600000 - 699999
     * 8  => 700000 - 799999
     * 9  => 800000 - 899999
     * 10 => 900000 - 999999
     * -------------------------------------------
     * @param  integer $number getInspectionTableNumber取得したテーブル番号
     * @return array   $ids    ランダム生成されたID
     */
    private function getCreateAppId($number)
    {
        $ids = [];

        if ($number == 1) {
            $min = 1;
            $max = 99999;
        } elseif (1 < $number && $number <= 10) {
            $min = ($number - 1) * 100000;
            $max = ($number * 100000) - 1;
        }

        for ($i = 0; $i < 10; $i++) {
            $ids[] = mt_rand($min, $max);
        }
        return $ids;
    }

    /**
     * アプリIDの候補がすでに使われているか判別し
     * 使われていないIDを1つだけ返す
     * --------------------------------------------
     * ■アプリIDを利用しているか判別について
     * applicationテーブルでの利用済みチェックと
     * game_applyテーブルでの利用済みチェックを行う
     * --------------------------------------------
     * @param  array   $ids         アプリIDの候補
     * @return integer $unUsedId[0] 利用されていないアプリID
     */
    private function getCheckAppId($ids)
    {
        $resApp    = $this->application->getListByIds($ids);
        $resApply  = $this->gameApply->getListByApplicationIds($ids);
        $usedIds   = [];

        foreach ($resApp as $app) {
            $usedIds[] = $app->id;
        }
        foreach ($resApply as $app) {
            $usedIds[] = $app->app_id;
        }
        $usedIds   = array_merge(array_unique($usedIds));
        $unUsedIds = array_merge(array_diff($ids, $usedIds));

        return $unUsedIds[0];
    }

//*********************************************************************************************************************
    /**
     * チェックボックス用配列取得：対応デバイス
     * @return array
     */
    public function getCheckDevice()
    {
        $config = config('forms.GameApply');
        return [
            $config['sp']          => $config['namesDevice'][$config['sp']],
            $config['pc']          => $config['namesDevice'][$config['pc']],
            $config['android']     => $config['namesDevice'][$config['android']],
            $config['apk_cloud']   => $config['namesDevice'][$config['apk_cloud']],
        ];
    }

    /**
     * ラジオボタン用配列取得：タイプ
     * @return array
     */
    public function getRadioType()
    {
        $config = config('forms.GameApply');
        return [
                $config['general']     => $config['namesType'][$config['general']],
                $config['adult']       => $config['namesType'][$config['adult']],
        ];
    }

    /**
     * ラジオボタン用配列取得：フラグ
     * @return array
     */
    public function getRadioFlag()
    {
        $config = config('forms.GameApply');
        return [
                $config['notExists']   => $config['namesFlag'][$config['notExists']],
                $config['exists']      => $config['namesFlag'][$config['exists']],
        ];
    }

    /**
     * ラジオボタン用配列取得：特商法表記名義
     * @return array
     */
    public function getRadioAsct()
    {
        $config = config('forms.GameApply');
        return [
                $config['ownCompany']  => $config['namesAsct'][$config['ownCompany']],
                $config['exnoa']         => $config['namesAsct'][$config['exnoa']],
        ];
    }

    /**
     * ラジオボタン用配列取得：連絡先情報
     * @return array
     */
    public function getRadioContact()
    {
        $config = config('forms.GameApply');
        return [
                $config['possible']    => $config['namesContact'][$config['possible']],
                $config['notPossible'] => $config['namesContact'][$config['notPossible']],
        ];
    }

//*********************************************************************************************************************
    /**
     * ページ設定取得
     * @return array
     */
    public function getFormData()
    {
        $config = config('forms.GameApply');
        return [
                'formData' => [
                        'screenName'  => $config['screenName'],
                        'breadcrumbs' => $config['breadcrumbsParent'],
                ],
                'namesStatus'         => $config['namesStatus'],
                'namesFlag'           => $config['namesFlag'],
                'namesType'           => $config['namesType'],
                'namesAsct'           => $config['namesAsct'],
                'namesIssue'          => $config['namesIssue'],
                'namesContact'        => $config['namesContact'],
                'namesDevice'         => $config['namesDevice'],

                'checkDevice'         => $this->getCheckDevice(),
                'radioType'           => $this->getRadioType(),
                'radioFlag'           => $this->getRadioFlag(),
                'radioAsct'           => $this->getRadioAsct(),
                'radioContact'        => $this->getRadioContact(),
        ];
    }

    /**
     * コントローラ名設定
     * @param string $controllerName
     */
    public function setControllerName($controllerName)
    {
        $this->controllerName = $controllerName;
    }
}
