<?php

namespace App\Services;

use App\Models\Freegame\Freegame;
use App\Models\Freegame\ApplicationExaminationUser;
use App\Models\Freegame\Application;
use App\Models\Freegame\User;
use App\Models\FreegameDeveloper\DeveloperApplication;

class PreUsersService extends CustomService
{
    protected $appExamUser;
    protected $application;
    protected $user;
    protected $developerApp;
    protected $controllerName;

    protected $selectApplicationList;

    public function __construct(
        ApplicationExaminationUser $appExamUser,
        Application                $application,
        User                       $user,
        DeveloperApplication       $developerApp
    ) {
        $this->appExamUser  = $appExamUser;
        $this->application  = $application;
        $this->user         = $user;
        $this->developerApp = $developerApp;
    }

//*********************************************************************************************************************
    /**
     * 詳細取得
     * @param  integer $appId
     * @param  integer $userId
     * @return object
     */
    public function getDetail($appId, $userId)
    {
        $preUserData = $this->appExamUser->getById($appId, $userId);

        return $preUserData;
    }

    /**
     * 仮詳細取得
     * @param  array $param
     * @return array
     */
    public function getMakeDetail($param)
    {
        $appId       = $param['app_id'];
        $userId      = $param['user_id'];
        $appData     = $this->application->getOne($appId);
        $userData    = $this->user->getOne($userId);

        return [
                'user_id'    => $userId,
                'nickname'   => $userData ? $userData->nickname   : '',
                'prefecture' => $userData ? $userData->prefecture : '',
                'gender'     => $userData ? $userData->gender     : '',
                'birth_date' => $userData ? $userData->birth_date : '',
                'blood_type' => $userData ? $userData->blood_type : '',
                'job'        => $userData ? $userData->job        : '',
                'hobby'      => $userData ? $userData->hobby      : '',
                'title'      => $appData  ? $appData->title       : '',
        ];
    }

//*********************************************************************************************************************
    /**
     * 検索一覧取得
     * @param  array $param
     * @return array
     */
    public function getSearchList($param)
    {
        // 検索条件設定
        if (request()->has('search')) {
            $param  = session("{$this->controllerName}.search", []);
            request()->merge($param);
        }
        if (! isset($param['perPage'])) {
            $param += ['perPage'      => config('forms.PreUsers.perPage')];
        }
        if (! isset($param['userSort']) || empty(config("forms.PreUsers.namesUserSortType.{$param['userSort']}"))) {
            $param['userSort']        = "date_desc";
        }
        $sort = config("forms.PreUsers.namesUserSortType.{$param['userSort']}.order");

        $param      = array_only($param, [
                'perPage',
                'user_id',
                'nickname',
                'app_id',
                'userSort',
        ]);
        $appends    = $param;
        request()->session()->set("{$this->controllerName}.search", $param);

         // その他条件
        if (auth_is_sap()) {
            $param += ['developer_id' => auth_user_id()];
        }
        $param     += ['status'       => config('forms.PreUsers.active')];

        // 取得
        $preUserList = $this->appExamUser->getList($param, $sort);
        $preUserList->appends($appends);
        $preUserList->userSort = $param['userSort'];

        return $preUserList;
    }

//*********************************************************************************************************************
    /**
     * セレクトボックス用ユーザタイトル一覧取得
     * @return array;
     */
    public function getSelectApplicationList()
    {
        if (isset($this->selectApplicationList)) {
            return $this->selectApplicationList;
        }

        if (auth_is_sap()) {
            $devList    = $this->developerApp->getApplicationAppIdList(['developer_id' => auth_user_id()]);
            $appIds     = [];

            foreach ($devList as $dev) {
                $appIds[$dev['app_id']] = $dev['app_id'];
            }
            $selectList = $this->application->getListTitleSortByBinaryTitle($appIds)->all();
        } else {
            $appList    = $this->application->getListAll();
            $selectList = [];

            foreach ($appList as $app) {
                $selectList[$app->id] = $app->title;
            }
        }

        $this->selectApplicationList = $selectList;
        return $selectList;
    }

//*********************************************************************************************************************
    /**
     * 登録
     * @param  array $param
     * @return boolean
     */
    public function createStore($param)
    {
        // 登録可否チェック
        if (! $this->isValidApp($param)) {
            return false;
        }

        Freegame::beginTransaction();
        try {
            // 事前登録
            $this->appExamUser->add($param);

            // ユーザType更新
            $userId = $param['user_id'];
            $param  = [
                    'type' => config('forms.PreUsers.developer'),
            ];
            $this->user->edit($userId, $param);

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 削除
     * @param  array $param
     * @return boolean
     */
    public function destroy($param)
    {
        // 削除可否チェック
        if (! $this->isValidApp($param)) {
            return false;
        }

        Freegame::beginTransaction();
        try {
            $appId  = $param['app_id'];
            $userId = $param['user_id'];

            // 登録削除
            $this->appExamUser->del($appId, $userId);

            // 残登録数確認
            $param  = [
                'user_id' => $userId,
            ];
            $users  = $this->appExamUser->getList($param);

            // 登録0ならユーザType更新
            if (! $users->count()) {
                $param  = [
                    'type' => '',
                ];
                $this->user->edit($userId, $param);
            }

            Freegame::commit();
        } catch (Exception $e) {
            Freegame::rollback();
            throw $e;
        }
        return true;
    }

//*********************************************************************************************************************
    /**
     * 有効ユーザチェック
     * @param  array $param
     * @return boolean
     */
    public function isExistingUser($param)
    {
        $userId   = $param['user_id'];
        $userData = $this->user->getOne($userId);

        if ($userData && ($userData->status == config('forms.PreUsers.active'))) {
            return true;
        }
        return false;
    }

    /**
     * 優待ユーザチェック
     * @param  array $param
     * @return boolean
     */
    public function isCourtesyUser($param)
    {
        $userId   = $param['user_id'];
        $userData = $this->user->getOne($userId);

        if ($userData && preg_match('/^D500/', $userData->member_id)) {
            return false;
        }
        return true;
    }

    /**
     * 登録済み設定チェック
     * @param  array $param
     * @return boolean
     */
    public function isDuplicationApp($param)
    {
        $appId    = $param['app_id'];
        $userId   = $param['user_id'];
        $preUser  = $this->getDetail($appId, $userId);

        if ($preUser) {
            return false;
        }
        return true;
    }

    /**
     * 有効アプリチェック
     * @param  array $param
     * @return boolean
     */
    public function isValidApp($param)
    {
        $appId    = $param['app_id'];
        $appList  = $this->getSelectApplicationList();

        if (isset($appList[$appId])) {
            return true;
        }
        return false;
    }

    /**
     * 編集可能チェック
     * @return boolean
     */
    public function isEnableEdit()
    {
        if (auth_is_sap()) {
            return true;
        }
        return false;
    }

//*********************************************************************************************************************
    /**
     * ページ設定取得
     * @return array
     */
    public function getFormData()
    {
        $config = config('forms.PreUsers');

        return [
                'formData' => [
                        'screenName'  => $config['screenName'],
                        'breadcrumbs' => $config['breadcrumbsParent'],
                ],
                'msgDelConfirm'       => $config['screenName'].config('forms.common.suffixBeforeDelete'),

                'namesUserStatus'     => $config['namesUserStatus'],
                'namesGender'         => $config['namesGender'],
                'namesBloodType'      => $config['namesBloodType'],
                'namesJob'            => $config['namesJob'],
                'namesPrefecture'     => $config['namesPrefecture'],

                'isEnableEdit'        => $this->isEnableEdit(),
        ];
    }

    /**
     * コントローラ名設定
     * @param string $controllerName
     */
    public function setControllerName($controllerName)
    {
        $this->controllerName = $controllerName;
    }

    /**
    * Get search params from request
    * @param request $request
    * @return array
    */
    public function requestUrl($request)
    {
        $request = array_only($request, [
            'userSort',
            'perPage',
            'userSort',
            'page'
        ]);
        return $request;
    }
}
