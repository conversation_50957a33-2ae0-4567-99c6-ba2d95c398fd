<?php
namespace App\Services;

use App\Models\FreegameContent\StampSet;
use App\Models\FreegameReport\ContentPointMonthlyReport;
use App\Models\Freegame\ContentOwner;
use App\Models\Freegame\UnitApplication;
use App\Models\FreegameDeveloper\ApplicationDivision;
use App\Models\FreegameDeveloper\ChApplicationDivision;
use App\Models\FreegameDeveloper\ClApplicationDivision;

class PfManagementContentsReportsMonthlyService extends CustomService
{
    protected $stampSet;
    protected $cpMonthlyReport;
    protected $contentOwner;
    protected $unitApp;
    protected $appDiv;
    protected $chAppDiv;
    protected $clAppDiv;

    const PF_CONTENT_UNIT_APP_ID = '0';
    const PF_CONTENT_APP_NAME = 'PFコンテンツ';

    public function __construct(
        StampSet                  $stampSet,
        ContentPointMonthlyReport $cpMonthlyReport,
        ContentOwner              $contentOwner,
        UnitApplication           $unitApp,
        ApplicationDivision       $appDiv,
        ChApplicationDivision     $chAppDiv,
        ClApplicationDivision     $clAppDiv
    ) {
        $this->stampSet        = $stampSet;
        $this->cpMonthlyReport = $cpMonthlyReport;
        $this->contentOwner    = $contentOwner;
        $this->unitApp         = $unitApp;
        $this->appDiv          = $appDiv;
        $this->chAppDiv        = $chAppDiv;
        $this->clAppDiv        = $clAppDiv;
    }

//*********************************************************************************************************************
    /**
     * CSV用データ一覧取得
     * @param  array $param
     * @return array
     */
    public function getCsvList($param)
    {
        if (auth_is_user_sap()) {
            $stampList = $this->stampSet->getList(['developer_id' => auth_user_id()]);
            $ids       = [];

            $contentOwnerList = $this->contentOwner->getList([], 'BINARY owner_name');
            foreach ($contentOwnerList as $contentOwner) {
                if ($stampList->contains('content_owner_id', $contentOwner->id)) {
                    $ids[] = $contentOwner->owner_id;
                }
            }
            $param    += ['owner_ids' => $ids];
        }
        $param         = array_only($param, [
                'report_date_begin',
                'report_date_end',
                'owner_ids',
        ]);
        $reportList    = $this->cpMonthlyReport->getList($param);

        foreach ($reportList as $reportData) {
            $this->getMakeCsv($reportData);
        }
        return $reportList;
    }

    /**
     * CSV用データ整形
     * @param  array $param
     * @return array
     */
    private function getMakeCsv($reportData)
    {
        $config = config('forms.PfManagementContentsReportsMonthly');

        $reportData->report_date = date('Y/m', strtotime($reportData->report_date));

        $reportData->type        = $config['namesType'][$config['stamp']];

        $reportData->device      = isset($config['namesDevice'][$reportData->device])
                                 ? $config['namesDevice'][$reportData->device] : '';

        $reportData->rate        = $reportData->rate ? $reportData->rate / 100 : '';

        $reportData->site        = ($reportData->site == $config['general'])
                                 ? $config['namesSite']['general'] : $config['namesSite']['adult'];

        // ゲームIDとタイトルの取得
        $contentOwnerId = $reportData->content_owner_id;
        $contentsOwner  = $this->contentOwner->getOneById($contentOwnerId);
        list($gameId, $appTitle) = $contentsOwner ? $this->getGameIdAndTitle($contentsOwner->unit_application_id) : [null, null];
        $reportData->game_id = $gameId;
        $reportData->app_title = $appTitle;
    }

    /**
     * unit_application_idからゲームIDとタイトル名を取得
     * @param int $unitAppId
     * @return string[]
     */
    private function getGameIdAndTitle($unitAppId)
    {
        $gameId = self::PF_CONTENT_UNIT_APP_ID;
        $appTitle = self::PF_CONTENT_APP_NAME;

        if ($unitAppId != 0) {
            $unitApp = $this->unitApp->getOne($unitAppId);
            $param = array('id' => array($unitAppId));
            switch ($unitApp->app_type) {
                case 'application':
                    $app = $this->unitApp->getListWithApplication($param)[0];
                    $appTitle = $app->title;
                    $gameId = $this->appDiv->getOneByAppId($app->app_id)->game_id;
                    break;

                case 'ch_application':
                    $app = $this->unitApp->getListWithChApplication($param)[0];
                    $appTitle = $app->title;
                    $gameId = $this->chAppDiv->getOneByChAppId($app->app_id)->game_id;
                    break;

                case 'cl_application':
                    $app = $this->unitApp->getListWithClApplication($param)[0];
                    $appTitle = $app->title;
                    $gameId = $this->clAppDiv->getOneByClAppId($app->app_id)->game_id;
                    break;

                default:
                    break;
            }
        }
        return array($gameId, $appTitle);
    }

//*********************************************************************************************************************
    /**
     * CSV用ヘッダデータ取得
     * @param  array $param
     * @return array
     */
    public function getCsvHeader()
    {
        $header['report_date']             = '日付';
        $header['type']                    = 'コンテンツ種別';
        $header['site']                    = 'ドメイン';
        $header['device']                  = 'デバイス';

        if (!auth_is_user_sap()) {
            $header['owner_id']            = 'オーナーID';
            $header['owner_name']          = 'オーナー名';
        }
        $header['maker_id']                = 'メーカーID';
        $header['maker_name']              = 'メーカー名';
        $header['game_id']                 = 'ゲームID';
        $header['app_title']               = 'タイトル';
        $header['product_id']              = 'コンテンツID';
        $header['content_name']            = 'コンテンツ名';
        $header['sell_count']              = '販売件数';

        if (!auth_is_user_sap()) {
            $header['without_tax_point']   = '販売単価(税抜)';
            $header['including_tax_point'] = '販売単価(税込)';
            $header['total_point']         = '売上金額(税抜)';
            $header['rate']                = '料率';
            $header['payment_point']       = '支払額(税抜)';
        } else {
            $header['including_tax_point'] = '販売単価(単価)';
            $header['total_point']         = '売上金額(税抜)';
        }
        return $header;
    }

    /**
     * CSV用ファイル名取得
     * @param  array $param
     * @return string
     */
    public function getCsvFileName($param)
    {
        $config          = config('forms.PfManagementContentsReportsMonthly');
        $reportDateBegin = isset($param['report_date_begin']) ? $param['report_date_begin'] : null;
        $reportDateEnd   = isset($param['report_date_end'])   ? $param['report_date_end']   : null;

        if (empty($reportDateBegin)) {
            $reportDateBegin = "{$config['minYear']}/01/01";
        }
        if (empty($reportDateEnd)) {
            $reportDateEnd   = date('Y/m/d');
        }
        $fileName        = sprintf(
            $config['csvFileName'],
            date('Y-m', strtotime($reportDateBegin)),
            date('Y-m', strtotime($reportDateEnd))
        );
        return $fileName;
    }

//*********************************************************************************************************************
    /**
     * セレクトボックス用配列取得：期間
     * @return array
     */
    public function getSelectPeriod()
    {
        $config = config('forms.PfManagementContentsReportsMonthly');

        return [
                $config['lastMonth']  => $config['namesPeriod'][$config['lastMonth']],
                $config['threeMonth'] => $config['namesPeriod'][$config['threeMonth']],
                $config['sixMonth']   => $config['namesPeriod'][$config['sixMonth']],
        ];
    }

//*********************************************************************************************************************
    /**
     * ページ設定取得
     * @return array
     */
    public function getFormData()
    {
        $config = config('forms.PfManagementContentsReportsMonthly');

        return [
                'formData' => [
                        'screenName'  => $config['screenName'],
                        'breadcrumbs' => $config['breadcrumbsParent'],
                ],
                'minYear'             => $config['minYear'],
                'maxYear'             => $config['maxYear'],
                'namesPeriod'         => $config['namesPeriod'],

                'selectPeriod'        => $this->getSelectPeriod(),
        ];
    }
}
