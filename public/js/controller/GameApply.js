$(function() {

    //----------------------------
    // ボタン関係
    //----------------------------
    // 基本
    $('a.submit-button').on('click', function(e) {
        e.preventDefault();
        var reqUrl  = $(this).attr('href');
        var reqForm = $('#frm_submit');

        reqForm.attr('action', reqUrl);
        reqForm.submit();
    });

    $('a.back-button').on('click', function(e) {
        e.preventDefault();
        var reqUrl  = $(this).attr('href');
        var reqForm = $('#frm_submit');

        reqForm.append('<input type="hidden" name="backFlag" value="1" />');
        reqForm.attr('action', reqUrl);
        reqForm.submit();
    });

    //----------------------------
    // 追加関係
    //----------------------------
    // テーブル要素
    $(document).on('click', 'a.add-table', function(e) {
        e.preventDefault();
        var area       = $(this).closest('.add-table-area');
        var base       = area.find('.base-table:first').clone();
        var count      = area.find('.base-table').length;
        var regexpName = new RegExp(/(.*?\[)[0-9]+(\].*)/, 'g');
        var regexpId   = new RegExp(/(.*?_)[0-9]+(_.*)/  , 'g');
        var element    = getAddElement(base, count, regexpName, regexpId);

        if (element.find('.base-users').length) {
            element.find('.base-users:gt(1)').remove();
            element.find('.del-user:first').addClass('is-disabled');
        }
        area.find('.base-table:last').after(element);
    });

    // Redmineユーザ要素
    $(document).on('click', 'a.add-users', function(e) {
        e.preventDefault();
        var area       = $(this).closest('.base-table').find('.add-users-area');
        var base       = area.find('.base-users:lt(2)').clone();
        var count      = area.find('.base-users').length / 2;
        var regexpName = new RegExp(/(.*\[)[0-9]+(\].*)/, 'g');
        var regexpId   = new RegExp(/(.*_)[0-9]+(_.*)/  , 'g');
        var element    = getAddElement(base, count, regexpName, regexpId);

        element.find('th').each(function() {
            var $this  = $(this);
            var name   = $this.html();
            var name   = name .replace(/(.*)[0-9]+/g, '$1' + (count + 1));

            $this.html(name);
        });
        area.find('.base-users:last').after(element);
    });

    // 要素取得
    function getAddElement(base, count, regexpName, regexpId)
    {
        base.find('a.is-disabled').removeClass('is-disabled');

        base.find('[type=text]').each(function() {
            var $this  = $(this);
            var name   = $this.attr('name');
            var name   = name .replace(regexpName, '$1' + count + '$2');

            $this.prop("disabled", false);
            $this.attr('name'    , name);
            $this.val ('');
            $this.closest('tr').removeClass('error');
        });

        base.find('[type=radio]').each(function() {
            var $this  = $(this);
            var name   = $this.attr('name');
            var name   = name .replace(regexpName, '$1' + count + '$2');
            var id     = $this.attr('id');
            var id     = id   .replace(regexpId  , '$1' + count + '$2');

            if ($this.index() == 0) {
                $this.prop('checked', true);
            } else {
                $this.prop('checked', false);
            }
            $this.prop("disabled", false);
            $this.attr('name'    , name);
            $this.attr('id'      , id);
            $this.next('label').attr('for', id);
            $this.closest('tr').removeClass('error');
        });
        return base;
    }

    //----------------------------
    // 削除関係
    //----------------------------
    // テーブル要素
    $(document).on('click', 'a.del-table', function(e) {
        e.preventDefault();
        var regexpName = new RegExp(/(.*?\[)[0-9]+(\].*)/, 'g');
        var regexpId   = new RegExp(/(.*?_)[0-9]+(_.*)/  , 'g');
        var myBlock    = $(this).closest('.base-table');
        var count      = 0;
        myBlock.remove();

        $('.add-table-area').find('.base-table').each(function() {
            var base   = $(this);

            base.find('input').each(function() {
                var $this  = $(this);
                var name   = $this.attr('name');
                var name   = name .replace(regexpName, '$1' + count + '$2');
                $this.attr('name', name);

                if ($this.attr('type') == 'radio') {
                    var id = $this.attr('id');
                    var id = id   .replace(regexpId  , '$1' + count + '$2');
                    $this.attr('id', id);
                    $this.next('label').attr('for', id);
                }
            });
            count++;
        });
    });

    // Redmineユーザ要素
    $(document).on('click', 'a.del-user', function(e) {
        e.preventDefault();
        var regexpName = new RegExp(/(.*\[)[0-9]+(\].*)/, 'g');
        var regexpTh   = new RegExp(/(.*)[0-9]+/        , 'g');
        var area       = $(this).closest('.add-users-area');
        var myBlock    = $(this).closest('tr');
        var myNext     = myBlock.next('tr');
        var count      = -1;
        myBlock.remove();
        myNext .remove();

        area.find('.base-users').each(function(i) {
            if (i % 2 == 0) {
                count++;
            }
            var $this  = $(this);
            var name   = $this.find('th')   .html();
            var name   = name .replace(regexpTh  , '$1' + (count + 1));
            $this.find('th')   .html(name);

            var name   = $this.find('input').attr('name');
            var name   = name .replace(regexpName, '$1' +  count + '$2');
            $this.find('input').attr('name', name);
        });
    });

    //----------------------------
    // 表示件数
    //----------------------------
    $('select[name="perPage"] option').filter(function() {
        return $(this).val() == $('select[name="perPage"]').data('value');
    }).prop('selected', true);

    $('select[name="perPage"]').on('change', function() {
        location.href = $(this).children('option[selected]').data('href');
    });
});
